# Option Combo Quote Cataloging

## Overview

This document explains how option combo quotes can be stored and retrieved from nautilus_trader's data catalog system. Combo quotes, whether synthetic (generated during backtesting) or native (from live brokers), can be cataloged just like any other market data.

## Technical Feasibility

### Catalog Architecture Support

The nautilus_trader catalog is **instrument-agnostic** and can store any `QuoteTick` data. The catalog organizes data hierarchically using instrument IDs:

```
data/
├── quotes/
│   └── INSTRUMENT_ID/
│       └── start_ts-end_ts.parquet
```

### Combo Quotes as Standard QuoteTicks

Synthetic combo quotes generated by `ComboMarketDataProvider` are regular `QuoteTick` objects:

```python
synthetic_quote = QuoteTick(
    instrument_id=combo.id,  # Auto-generated combo instrument ID
    bid_price=combo_bid_price,
    ask_price=combo_ask_price,
    bid_size=min_bid_size,
    ask_size=min_ask_size,
    ts_event=ts_event,
    ts_init=self._clock.timestamp_ns(),
)
```

## Combo Instrument ID Generation

### Algorithm Details

Combo instrument IDs are automatically generated using a sophisticated algorithm:

1. **Leg Sorting**: Put before Call, descending expiry, ascending strikes
2. **Component Formatting**: `{ratio}{underlying}{expiry}{type}{strike}`
3. **Ratio Formatting**: Positive as number, negative in parentheses
4. **Venue Consistency**: All legs must have same venue

### Format Structure

```
{ratio}{underlying}{YYMMDD}{C/P}{strike*1000}/{ratio}{underlying}{YYMMDD}{C/P}{strike*1000}.venue
```

### Examples

#### Straddle
- **Legs**: +1 SPY 240315 P 500, +1 SPY 240315 C 500
- **Generated ID**: `1SPY240315P00500000/1SPY240315C00500000.OPRA`

#### Iron Condor
- **Legs**: +1 SPY 240315 P 480, -1 SPY 240315 P 490, -1 SPY 240315 C 510, +1 SPY 240315 C 520
- **Generated ID**: `1SPY240315P00480000/(1)SPY240315P00490000/(1)SPY240315C00510000/1SPY240315C00520000.OPRA`

## Data Flow Architecture

```mermaid
graph TB
    subgraph "Backtesting Environment"
        A[Individual Leg Quotes] --> B[ComboMarketDataProvider]
        B --> C[Synthetic Combo Quotes]
        C --> D[Catalog Storage]
    end
    
    subgraph "Live Trading Environment"
        E[Broker Native Combo Quotes] --> F[Data Client]
        F --> G[Native Combo Quotes]
        G --> D
    end
    
    subgraph "Catalog System"
        D --> H[Parquet Files]
        H --> I[Query Interface]
        I --> J[Historical Analysis]
        I --> K[Backtesting Replay]
    end
```

## Storage Structure

### Directory Organization

```
data/quotes/
├── SPY240315P00480000.OPRA/                           # Individual legs
├── SPY240315C00520000.OPRA/                           # Individual legs
├── 1SPY240315P00500000/1SPY240315C00500000.OPRA/     # Straddle combo
└── 1SPY240315P00480000/(1)SPY240315P00490000/(1)SPY240315C00510000/1SPY240315C00520000.OPRA/  # Iron Condor
```

### File Structure

Each combo quote directory contains time-partitioned Parquet files:
```
1SPY240315P00500000/1SPY240315C00500000.OPRA/
├── 20240101_000000-20240101_235959.parquet
├── 20240102_000000-20240102_235959.parquet
└── ...
```

## Implementation Approach

### Storing Combo Quotes

#### During Backtesting
```python
class ComboMarketDataProvider:
    def on_quote_tick(self, tick: QuoteTick) -> QuoteTick | None:
        # Generate synthetic quote
        synthetic_quote = self._generate_combo_quote(combo)
        if synthetic_quote:
            # Store in catalog if configured
            if self._catalog:
                self._catalog.write_data([synthetic_quote])
            return synthetic_quote
```

#### During Live Trading
```python
class InteractiveBrokersDataClient:
    def on_quote_tick_received(self, ib_tick):
        # Convert IB combo quote to nautilus QuoteTick
        combo_quote = self._convert_ib_combo_quote(ib_tick)
        
        # Store in catalog if configured
        if self._catalog:
            self._catalog.write_data([combo_quote])
```

### Querying Combo Quotes

#### Direct Query (Exact ID)
```python
combo_quotes = catalog.query(
    data_cls=QuoteTick,
    identifiers=["1SPY240315P00500000/1SPY240315C00500000.OPRA"],
    start="2024-01-01T00:00:00Z",
    end="2024-01-02T00:00:00Z"
)
```

#### Enhanced Query System

Due to complex auto-generated IDs, a registry system is recommended:

```python
class ComboRegistry:
    """Registry to map combo characteristics to generated instrument IDs."""
    
    def register_combo(self, combo: OptionCombo) -> None:
        """Register a combo and its generated ID."""
        self._combos[combo.id] = {
            'underlying': combo.underlying,
            'strategy_type': combo.strategy_type,
            'legs': [(leg.instrument_id, leg.ratio) for leg in combo.legs],
            'expiry_range': (min_expiry, max_expiry),
        }
    
    def find_combos(self, underlying: str = None, strategy_type: str = None) -> list[InstrumentId]:
        """Find combo instrument IDs by characteristics."""
        # Implementation to search registered combos
```

## Benefits of Cataloging Combo Quotes

### Historical Analysis
- **Backtesting**: Replay exact combo market conditions
- **Strategy Development**: Analyze combo spread behavior over time
- **Performance Attribution**: Compare synthetic vs actual combo pricing

### Research and Development
- **Model Validation**: Compare synthetic pricing models against real market data
- **Spread Analysis**: Study combo bid-ask spreads and liquidity patterns
- **Market Microstructure**: Analyze combo vs leg pricing relationships

### Operational Benefits
- **Audit Trail**: Complete record of combo pricing decisions
- **Debugging**: Replay exact market conditions that caused issues
- **Compliance**: Historical record of combo quote generation

## Implementation Considerations

### Storage Efficiency
- **Combo quotes are sparse**: Generated only when all legs have quotes
- **Smaller data volume**: Fewer combo quotes than individual leg quotes
- **Efficient compression**: Parquet format handles sparse data well

### Data Consistency
- **Timestamp alignment**: Combo quotes use leg quote timestamps
- **Instrument registration**: Combo instruments must be in catalog
- **Metadata preservation**: Store combo construction details

### Query Performance
- **Instrument-specific queries**: Fast retrieval by combo instrument ID
- **Time-range filtering**: Efficient temporal queries
- **Cross-instrument analysis**: Join combo and leg data

## Usage Examples

### Backtesting with Historical Combo Quotes
```python
# Use stored combo quotes instead of regenerating
combo_quotes = catalog.query(
    data_cls=QuoteTick,
    identifiers=["1SPY240315P00500000/1SPY240315C00500000.OPRA"],
    start=backtest_start,
    end=backtest_end
)
# Feed directly to backtest engine
```

### Live vs Synthetic Comparison
```python
# Compare live IB combo quotes with synthetic generation
live_quotes = catalog.query(
    data_cls=QuoteTick,
    identifiers=["1SPY240315P00500000/1SPY240315C00500000.OPRA"],
    source="live"
)
synthetic_quotes = catalog.query(
    data_cls=QuoteTick,
    identifiers=["1SPY240315P00500000/1SPY240315C00500000.OPRA"],
    source="synthetic"
)
```

## Detailed Technical Architecture

### Combo Quote Generation Flow

```mermaid
sequenceDiagram
    participant LegQuotes as Leg Quote Sources
    participant Provider as ComboMarketDataProvider
    participant Cache as Quote Cache
    participant Catalog as Data Catalog
    participant Engine as Backtest Engine

    LegQuotes->>Provider: QuoteTick (Leg)
    Provider->>Cache: Store Leg Quote
    Provider->>Provider: Check Combo Subscriptions
    Provider->>Provider: Generate Synthetic Quote
    Provider->>Cache: Store Combo Quote
    Provider->>Catalog: Write Combo Quote (Optional)
    Provider->>Engine: Publish Combo Quote
```

### Instrument ID Generation Process

```mermaid
flowchart TD
    A[Combo Legs] --> B[Sort Legs]
    B --> C{Put before Call}
    C --> D{Descending Expiry}
    D --> E{Ascending Strike}
    E --> F[Format Components]
    F --> G[Build Symbol Parts]
    G --> H[Join with '/']
    H --> I[Add Venue]
    I --> J[Final Instrument ID]

    subgraph "Formatting Rules"
        K[Positive Ratio: '1']
        L[Negative Ratio: '(1)']
        M[Strike: multiply by 1000]
        N[Expiry: YYMMDD format]
    end
```

### Catalog Storage Architecture

```mermaid
graph LR
    subgraph "Data Sources"
        A[Synthetic Quotes]
        B[Live Broker Quotes]
    end

    subgraph "Catalog Layer"
        C[ParquetDataCatalog]
        D[Object Store]
        E[Compression Engine]
    end

    subgraph "Storage Structure"
        F[Time-Partitioned Files]
        G[Instrument Directories]
        H[Metadata Storage]
    end

    subgraph "Query Interface"
        I[Direct ID Query]
        J[Pattern Matching]
        K[Registry Lookup]
    end

    A --> C
    B --> C
    C --> D
    D --> E
    E --> F
    F --> G
    G --> H

    C --> I
    C --> J
    C --> K
```

## Advanced Implementation Details

### Registry-Based Discovery System

```python
class ComboQuoteRegistry:
    """Advanced registry for combo quote discovery and management."""

    def __init__(self, catalog: ParquetDataCatalog):
        self._catalog = catalog
        self._combo_metadata = {}
        self._underlying_index = defaultdict(set)
        self._strategy_index = defaultdict(set)
        self._expiry_index = defaultdict(set)

    def register_combo(self, combo: OptionCombo) -> None:
        """Register combo with multiple indices for fast lookup."""
        combo_id = str(combo.id)

        metadata = {
            'instrument_id': combo_id,
            'underlying': combo.underlying,
            'strategy_type': combo.strategy_type,
            'legs': [(str(leg.instrument_id), leg.ratio) for leg in combo.legs],
            'expiry_range': self._get_expiry_range(combo),
            'strike_range': self._get_strike_range(combo),
            'registration_time': time.time(),
        }

        # Store metadata
        self._combo_metadata[combo_id] = metadata

        # Update indices
        self._underlying_index[combo.underlying].add(combo_id)
        self._strategy_index[combo.strategy_type].add(combo_id)

        for expiry in self._get_expiries(combo):
            self._expiry_index[expiry].add(combo_id)

    def find_combos(
        self,
        underlying: str = None,
        strategy_type: str = None,
        expiry_start: str = None,
        expiry_end: str = None,
        strike_min: float = None,
        strike_max: float = None,
    ) -> list[str]:
        """Find combo instrument IDs using multiple criteria."""
        candidates = set(self._combo_metadata.keys())

        # Filter by underlying
        if underlying:
            candidates &= self._underlying_index[underlying]

        # Filter by strategy type
        if strategy_type:
            candidates &= self._strategy_index[strategy_type]

        # Filter by expiry range
        if expiry_start or expiry_end:
            expiry_candidates = set()
            for expiry, combo_ids in self._expiry_index.items():
                if self._expiry_in_range(expiry, expiry_start, expiry_end):
                    expiry_candidates.update(combo_ids)
            candidates &= expiry_candidates

        # Filter by strike range
        if strike_min is not None or strike_max is not None:
            candidates = {
                combo_id for combo_id in candidates
                if self._strike_in_range(
                    self._combo_metadata[combo_id]['strike_range'],
                    strike_min,
                    strike_max
                )
            }

        return list(candidates)

    def get_combo_metadata(self, combo_id: str) -> dict:
        """Get detailed metadata for a combo."""
        return self._combo_metadata.get(combo_id, {})
```

### Enhanced Query Interface

```python
class ComboQuoteQuery:
    """High-level interface for querying combo quotes."""

    def __init__(self, catalog: ParquetDataCatalog, registry: ComboQuoteRegistry):
        self._catalog = catalog
        self._registry = registry

    def query_by_characteristics(
        self,
        underlying: str,
        strategy_type: str = None,
        start: str = None,
        end: str = None,
        **filters
    ) -> list[QuoteTick]:
        """Query combo quotes by characteristics rather than exact IDs."""

        # Find matching combo IDs
        combo_ids = self._registry.find_combos(
            underlying=underlying,
            strategy_type=strategy_type,
            **filters
        )

        if not combo_ids:
            return []

        # Query catalog for all matching combos
        all_quotes = []
        for combo_id in combo_ids:
            try:
                quotes = self._catalog.query(
                    data_cls=QuoteTick,
                    identifiers=[combo_id],
                    start=start,
                    end=end
                )
                all_quotes.extend(quotes)
            except Exception as e:
                logger.warning(f"Failed to query combo {combo_id}: {e}")

        return sorted(all_quotes, key=lambda q: q.ts_event)

    def query_combo_vs_legs(
        self,
        combo_id: str,
        start: str = None,
        end: str = None
    ) -> dict:
        """Query both combo quotes and individual leg quotes for comparison."""

        # Get combo metadata
        metadata = self._registry.get_combo_metadata(combo_id)
        if not metadata:
            raise ValueError(f"Combo not found in registry: {combo_id}")

        # Query combo quotes
        combo_quotes = self._catalog.query(
            data_cls=QuoteTick,
            identifiers=[combo_id],
            start=start,
            end=end
        )

        # Query leg quotes
        leg_ids = [leg[0] for leg in metadata['legs']]
        leg_quotes = {}
        for leg_id in leg_ids:
            leg_quotes[leg_id] = self._catalog.query(
                data_cls=QuoteTick,
                identifiers=[leg_id],
                start=start,
                end=end
            )

        return {
            'combo_quotes': combo_quotes,
            'leg_quotes': leg_quotes,
            'metadata': metadata
        }
```

## Performance Considerations

### Storage Optimization

```mermaid
graph TB
    subgraph "Storage Efficiency"
        A[Sparse Data Handling]
        B[Parquet Compression]
        C[Time Partitioning]
        D[Columnar Storage]
    end

    subgraph "Query Optimization"
        E[Index-Based Lookup]
        F[Metadata Caching]
        G[Parallel Queries]
        H[Result Streaming]
    end

    subgraph "Memory Management"
        I[Lazy Loading]
        J[Batch Processing]
        K[Cache Eviction]
        L[Resource Pooling]
    end

    A --> E
    B --> F
    C --> G
    D --> H
    E --> I
    F --> J
    G --> K
    H --> L
```

### Scalability Metrics

| Aspect | Typical Performance | Optimization Strategy |
|--------|-------------------|----------------------|
| Storage Size | 10-20% of leg quotes | Sparse data compression |
| Query Latency | <100ms for single combo | Index-based lookup |
| Memory Usage | <1GB for 1M quotes | Streaming queries |
| Concurrent Queries | 50+ simultaneous | Connection pooling |

## Conclusion

Catalog quotes for combos are not only possible but highly valuable. The existing catalog architecture already supports this functionality through its instrument-agnostic design. While the complex auto-generated instrument IDs require sophisticated discovery and querying mechanisms, the benefits for historical analysis, research, and operational audit trails make this a worthwhile enhancement.

The implementation would be straightforward since combo quotes are standard `QuoteTick` objects that the catalog already knows how to handle, requiring only integration points in the combo data providers and live data clients.

Key implementation components:
- **Registry system** for combo discovery and metadata management
- **Enhanced query interface** supporting characteristic-based searches
- **Performance optimizations** for storage efficiency and query speed
- **Comprehensive tooling** for combo vs leg analysis
