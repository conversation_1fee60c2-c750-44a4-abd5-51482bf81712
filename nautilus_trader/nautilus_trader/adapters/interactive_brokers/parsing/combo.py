# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

"""
Interactive Brokers combo order parsing and conversion utilities.
"""

from typing import TYPE_CHECKING

from nautilus_trader.adapters.interactive_brokers.common import ComboLeg as IBComboLeg
from nautilus_trader.adapters.interactive_brokers.common import IBContract
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments.option_combo import ComboLeg
from nautilus_trader.model.instruments.option_combo import OptionCombo

if TYPE_CHECKING:
    from nautilus_trader.adapters.interactive_brokers.providers import InteractiveBrokersInstrumentProvider


def option_combo_to_ib_contract(
    combo: OptionCombo,
    instrument_provider: "InteractiveBrokersInstrumentProvider",
) -> IBContract:
    """
    Convert an OptionCombo instrument to an Interactive Brokers BAG contract.
    
    Parameters
    ----------
    combo : OptionCombo
        The option combo instrument to convert.
    instrument_provider : InteractiveBrokersInstrumentProvider
        The instrument provider to resolve leg contract IDs.
        
    Returns
    -------
    IBContract
        The IB BAG contract.
        
    Raises
    ------
    ValueError
        If any leg instrument is not found in the instrument provider.
    """
    # Create combo legs for IB
    ib_combo_legs = []
    for leg in combo.legs:
        # Get the contract ID for this leg instrument
        try:
            leg_details = instrument_provider.contract_details[leg.instrument_id]
            leg_con_id = leg_details.contract.conId
        except KeyError:
            raise ValueError(f"Leg instrument {leg.instrument_id} not found in instrument provider")

        # Convert signed ratio to IB action and ratio
        # Positive ratio = BUY, negative ratio = SELL
        ib_action = "BUY" if leg.ratio > 0 else "SELL"
        ib_ratio = abs(leg.ratio)

        ib_combo_leg = IBComboLeg(
            conId=leg_con_id,
            ratio=ib_ratio,
            action=ib_action,
            exchange="SMART",  # Use SMART routing for combo legs
        )
        ib_combo_legs.append(ib_combo_leg)
    
    # Create the BAG contract
    bag_contract = IBContract(
        secType="BAG",
        symbol=combo.underlying,
        currency=combo.quote_currency.code,
        exchange="SMART",
        comboLegs=ib_combo_legs,
        comboLegsDescrip=f"{combo.strategy_type} {combo.underlying}",
    )
    
    return bag_contract


def ib_contract_to_option_combo(
    ib_contract: IBContract,
    instrument_id: InstrumentId,
    instrument_provider: "InteractiveBrokersInstrumentProvider",
) -> OptionCombo:
    """
    Convert an Interactive Brokers BAG contract to an OptionCombo instrument.
    
    Parameters
    ----------
    ib_contract : IBContract
        The IB BAG contract.
    instrument_id : InstrumentId
        The Nautilus instrument ID for the combo.
    instrument_provider : InteractiveBrokersInstrumentProvider
        The instrument provider to resolve leg instruments.
        
    Returns
    -------
    OptionCombo
        The option combo instrument.
        
    Raises
    ------
    ValueError
        If the contract is not a BAG type or leg instruments cannot be resolved.
    """
    if ib_contract.secType != "BAG":
        raise ValueError(f"Expected BAG contract, got {ib_contract.secType}")
    
    if not ib_contract.comboLegs:
        raise ValueError("BAG contract has no combo legs")
    
    # Convert IB combo legs to Nautilus combo legs
    combo_legs = []
    for ib_leg in ib_contract.comboLegs:
        # Resolve the leg instrument from contract ID
        try:
            leg_instrument = instrument_provider.get_instrument(ib_leg.conId)
            leg_instrument_id = leg_instrument.id
        except Exception:
            # If we can't resolve the instrument, create a placeholder
            # In practice, this should be handled more robustly
            leg_instrument_id = InstrumentId(
                symbol=f"LEG_{ib_leg.conId}",
                venue=instrument_id.venue,
            )
        
        # Convert IB action and ratio to signed ratio
        # BUY = positive, SELL = negative
        signed_ratio = ib_leg.ratio if ib_leg.action == "BUY" else -ib_leg.ratio

        combo_leg = ComboLeg(
            instrument_id=leg_instrument_id,
            ratio=signed_ratio,
        )
        combo_legs.append(combo_leg)
    
    # Extract strategy type from description
    strategy_type = "COMBO"  # Default
    if ib_contract.comboLegsDescrip:
        desc = ib_contract.comboLegsDescrip.upper()
        if "IRON CONDOR" in desc or "IRON_CONDOR" in desc:
            strategy_type = "IRON_CONDOR"
        elif "BUTTERFLY" in desc:
            strategy_type = "BUTTERFLY"
        elif "STRADDLE" in desc:
            strategy_type = "STRADDLE"
        elif "STRANGLE" in desc:
            strategy_type = "STRANGLE"
    
    # Create the OptionCombo instrument
    # Note: Some values are defaults since BAG contracts don't have all the info
    from nautilus_trader.model.enums import AssetClass
    from nautilus_trader.model.identifiers import Symbol
    from nautilus_trader.model.objects import Currency, Price, Quantity
    import time
    
    timestamp = time.time_ns()
    
    return OptionCombo(
        raw_symbol=Symbol(ib_contract.localSymbol or ib_contract.symbol),
        asset_class=AssetClass.EQUITY,  # Most option combos are equity-based
        currency=Currency.from_str(ib_contract.currency),
        price_precision=2,  # Default precision
        price_increment=Price(0.01, 2),  # Default increment
        multiplier=Quantity.from_str(ib_contract.multiplier or "100"),
        lot_size=Quantity.from_int(1),
        underlying=ib_contract.symbol,
        strategy_type=strategy_type,
        legs=combo_legs,
        vega_multiplier=1.0,  # Default value
        activation_ns=0,  # BAG contracts don't have activation dates
        expiration_ns=0,  # BAG contracts don't have single expiration dates
        ts_event=timestamp,
        ts_init=timestamp,
        instrument_id=instrument_id,  # Use explicit instrument_id
    )


def create_combo_fill_reports(
    combo_fill: dict,
    combo: OptionCombo,
    instrument_provider: "InteractiveBrokersInstrumentProvider",
) -> list[dict]:
    """
    Convert a combo fill from IB into individual leg fill reports.
    
    Parameters
    ----------
    combo_fill : dict
        The combo fill information from IB.
    combo : OptionCombo
        The combo instrument.
    instrument_provider : InteractiveBrokersInstrumentProvider
        The instrument provider.
        
    Returns
    -------
    list[dict]
        List of individual leg fill reports.
    """
    leg_fills = []
    
    # For each leg in the combo, create a proportional fill
    combo_quantity = combo_fill.get('quantity', 0)
    combo_price = combo_fill.get('price', 0.0)
    
    for leg in combo.legs:
        # Calculate leg-specific fill details
        leg_quantity = combo_quantity * leg.ratio
        
        # For leg price, we would need market data or use a proportional approach
        # This is a simplified implementation
        leg_price = combo_price * leg.weight if hasattr(leg, 'weight') else combo_price
        
        leg_fill = {
            'instrument_id': leg.instrument_id,
            'side': leg.side,
            'quantity': leg_quantity,
            'price': leg_price,
            'commission': combo_fill.get('commission', 0.0) / len(combo.legs),  # Split commission
            'timestamp': combo_fill.get('timestamp'),
        }
        leg_fills.append(leg_fill)
    
    return leg_fills
