"""
Combo order execution handling for backtest environments.

This module provides functionality to handle combo order execution and fill
decomposition in backtest environments through message bus interception.
"""

from typing import Dict, List

from nautilus_trader.backtest.combo_execution import ComboBacktestExecutionHandler
from nautilus_trader.cache.cache import Cache
from nautilus_trader.common.component import Logger
from nautilus_trader.common.component import MessageBus
from nautilus_trader.core.message import Event
from nautilus_trader.model.events import OrderFilled
from nautilus_trader.model.identifiers import ClientOrderId
from nautilus_trader.model.instruments.option_combo import OptionCombo


class ComboFillHandler:
    """
    Handles combo fill events by intercepting them from the message bus
    and decomposing them into individual leg fills.
    """

    def __init__(
        self,
        msgbus: MessageBus,
        cache: Cache,
        logger: Logger,
    ):
        self._msgbus = msgbus
        self._cache = cache
        self._logger = logger
        self._combo_handler = ComboBacktestExecutionHandler(cache, logger)
        self._processed_fills: set = set()

        # Subscribe to all order fill events
        self._msgbus.subscribe("events.order.*", self._handle_order_event)

    def _handle_order_event(self, event: Event) -> None:
        """
        Handle order events and intercept combo fills.

        Parameters
        ----------
        event : Event
            The order event to handle.
        """
        if not isinstance(event, OrderFilled):
            return

        # Avoid processing the same fill multiple times
        if hasattr(event, 'event_id') and event.event_id in self._processed_fills:
            return

        if hasattr(event, 'event_id'):
            self._processed_fills.add(event.event_id)

        # Check if this is a combo instrument
        instrument = self._cache.instrument(event.instrument_id)
        if not isinstance(instrument, OptionCombo):
            return  # Not a combo order

        self._logger.info(
            f"Intercepting combo fill: {event.instrument_id} "
            f"{event.order_side} {event.last_qty} @ {event.last_px}"
        )

        # Register combo order
        self._combo_handler.register_combo_order(event.client_order_id, instrument)

        # Decompose combo fill into leg fills
        leg_fills = self._combo_handler.handle_combo_fill(event)

        # Publish leg fills to the message bus
        for leg_fill in leg_fills:
            self._logger.info(
                f"Publishing leg fill: {leg_fill.instrument_id} "
                f"{leg_fill.order_side} {leg_fill.last_qty} @ {leg_fill.last_px}"
            )

            # Publish the leg fill event to the same strategy
            topic = f"events.order.{leg_fill.strategy_id}"
            self._msgbus.publish(topic, leg_fill)

        if leg_fills:
            self._logger.info(
                f"Decomposed combo fill into {len(leg_fills)} leg fills"
            )


class ComboOrderTracker:
    """
    Tracks combo orders and their associated leg orders for backtest execution.
    
    This class helps coordinate between combo orders submitted by strategies
    and the actual execution in the backtest engine.
    """
    
    def __init__(self):
        self._combo_orders: Dict[ClientOrderId, OptionCombo] = {}
        self._leg_to_combo: Dict[ClientOrderId, ClientOrderId] = {}
        
    def register_combo_order(self, combo_order_id: ClientOrderId, combo: OptionCombo):
        """Register a combo order for tracking."""
        self._combo_orders[combo_order_id] = combo
        
    def register_leg_order(self, leg_order_id: ClientOrderId, combo_order_id: ClientOrderId):
        """Register a leg order as part of a combo."""
        self._leg_to_combo[leg_order_id] = combo_order_id
        
    def get_combo_for_leg(self, leg_order_id: ClientOrderId) -> ClientOrderId | None:
        """Get the combo order ID for a leg order."""
        return self._leg_to_combo.get(leg_order_id)
        
    def get_combo_instrument(self, combo_order_id: ClientOrderId) -> OptionCombo | None:
        """Get the combo instrument for a combo order."""
        return self._combo_orders.get(combo_order_id)
        
    def is_combo_order(self, order_id: ClientOrderId) -> bool:
        """Check if an order is a combo order."""
        return order_id in self._combo_orders
        
    def is_leg_order(self, order_id: ClientOrderId) -> bool:
        """Check if an order is a leg order."""
        return order_id in self._leg_to_combo



