"""
Combo order execution handling for backtest environments.

This module provides functionality to:
1. Process combo orders as single units in backtest
2. Decompose combo fills into individual leg fills
3. Ensure price consistency between combo and leg fills
4. Handle partial fills and order state management
"""

from decimal import Decimal
from typing import Dict, List, Optional

from nautilus_trader.backtest.combo_fill_model import ComboFillModel
from nautilus_trader.cache.cache import Cache
from nautilus_trader.common.component import Logger
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.events import OrderFilled
from nautilus_trader.model.identifiers import ClientOrderId
from nautilus_trader.model.identifiers import TradeId
from nautilus_trader.model.identifiers import VenueOrderId
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.objects import Money
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity
from nautilus_trader.model.orders import Order


class ComboFillDecomposer:
    """
    Decomposes combo fills into individual leg fills for backtest environments.
    
    This class ensures that:
    1. Combo fills are properly decomposed into leg fills
    2. The sum of leg execution prices weighted by ratios equals combo price
    3. Commission is properly allocated across legs
    4. All fills maintain proper timestamps and metadata
    """
    
    def __init__(
        self,
        cache: Cache,
        logger: Logger,
    ):
        self._cache = cache
        self._logger = logger
        
    def decompose_combo_fill(self, combo_fill: OrderFilled) -> List[OrderFilled]:
        """
        Decompose a combo fill into individual leg fills.
        
        Parameters
        ----------
        combo_fill : OrderFilled
            The combo fill event to decompose.
            
        Returns
        -------
        List[OrderFilled]
            List of individual leg fill events.
            
        Raises
        ------
        ValueError
            If combo instrument not found or invalid fill data.
        """
        # Get combo instrument from cache
        combo_instrument = self._cache.instrument(combo_fill.instrument_id)
        if not isinstance(combo_instrument, OptionCombo):
            raise ValueError(f"Expected OptionCombo, got {type(combo_instrument)}")
            
        self._logger.info(
            f"Decomposing combo fill: {combo_fill.instrument_id} "
            f"{combo_fill.order_side} {combo_fill.last_qty} @ {combo_fill.last_px}"
        )
        
        # Get current market prices for legs
        leg_prices = self._get_leg_market_prices(combo_instrument)
        
        # Calculate leg fills
        leg_fills = []
        total_weighted_price = Decimal('0')
        total_commission = combo_fill.commission.as_decimal()
        
        for i, leg in enumerate(combo_instrument.legs):
            leg_fill = self._create_leg_fill(
                combo_fill=combo_fill,
                leg=leg,
                leg_index=i,
                leg_market_price=leg_prices.get(leg.instrument_id),
                leg_commission=total_commission / len(combo_instrument.legs),
            )
            
            leg_fills.append(leg_fill)
            
            # Track weighted price for validation
            weight = abs(leg.ratio)
            total_weighted_price += Decimal(str(leg_fill.last_px)) * weight
            
        # Validate price consistency
        combo_price = Decimal(str(combo_fill.last_px))
        price_diff = abs(total_weighted_price - combo_price)
        
        if price_diff > Decimal('0.01'):  # Allow 1 cent tolerance
            self._logger.warning(
                f"Price inconsistency in combo decomposition: "
                f"combo={combo_price}, weighted_legs={total_weighted_price}, "
                f"diff={price_diff}"
            )
            
        self._logger.info(
            f"Created {len(leg_fills)} leg fills from combo fill"
        )
        
        return leg_fills
        
    def _get_leg_market_prices(self, combo: OptionCombo) -> Dict:
        """Get current market prices for combo legs."""
        leg_prices = {}
        
        for leg in combo.legs:
            # Try to get current quote
            quote = self._cache.quote_tick(leg.instrument_id)
            if quote is not None:
                # Use mid price as fair value
                mid_price_decimal = (quote.bid_price.as_decimal() + quote.ask_price.as_decimal()) / 2
                mid_price = Price(mid_price_decimal, precision=quote.bid_price.precision)
                leg_prices[leg.instrument_id] = mid_price
            else:
                # Fallback to last trade price
                trade = self._cache.trade_tick(leg.instrument_id)
                if trade is not None:
                    leg_prices[leg.instrument_id] = trade.price
                    
        return leg_prices
        
    def _create_leg_fill(
        self,
        combo_fill: OrderFilled,
        leg,
        leg_index: int,
        leg_market_price: Optional[Price],
        leg_commission: Decimal,
    ) -> OrderFilled:
        """Create a leg fill from combo fill data."""
        
        # Determine leg side based on combo side and leg ratio
        if combo_fill.order_side == OrderSide.BUY:
            leg_side = OrderSide.BUY if leg.ratio > 0 else OrderSide.SELL
        else:
            leg_side = OrderSide.SELL if leg.ratio > 0 else OrderSide.BUY
            
        # Calculate leg quantity
        leg_quantity = Quantity(
            abs(leg.ratio) * combo_fill.last_qty.as_decimal(),
            precision=combo_fill.last_qty.precision,
        )
        
        # Determine leg price
        if leg_market_price is not None:
            leg_price = leg_market_price
        else:
            # Fallback: estimate from combo price
            # This is a simplified approach - in practice you'd want more sophisticated pricing
            estimated_price = float(combo_fill.last_px) / len(combo_fill.instrument_id.value.split('/'))
            leg_price = Price.from_str(f"{estimated_price:.2f}")
            
        # Create leg fill event
        leg_fill = OrderFilled(
            trader_id=combo_fill.trader_id,
            strategy_id=combo_fill.strategy_id,
            instrument_id=leg.instrument_id,
            client_order_id=ClientOrderId(f"{combo_fill.client_order_id.value}_LEG{leg_index}"),
            venue_order_id=VenueOrderId(f"{combo_fill.venue_order_id.value}_LEG{leg_index}"),
            account_id=combo_fill.account_id,
            trade_id=TradeId(f"{combo_fill.trade_id.value}_LEG{leg_index}"),
            order_side=leg_side,
            order_type=combo_fill.order_type,
            last_qty=leg_quantity,
            last_px=leg_price,
            currency=combo_fill.currency,
            liquidity_side=combo_fill.liquidity_side,
            event_id=UUID4(),
            ts_event=combo_fill.ts_event,
            ts_init=combo_fill.ts_init,
            reconciliation=combo_fill.reconciliation,
            position_id=combo_fill.position_id,
            commission=Money(leg_commission, combo_fill.commission.currency) if combo_fill.commission else None,
        )
        
        return leg_fill


class ComboBacktestExecutionHandler:
    """
    Handles combo order execution in backtest environments.
    
    This class integrates with the backtest engine to:
    1. Process combo orders as single units
    2. Generate appropriate fills
    3. Decompose fills into leg components
    4. Maintain proper order state
    """
    
    def __init__(
        self,
        cache: Cache,
        logger: Logger,
    ):
        self._cache = cache
        self._logger = logger
        self._decomposer = ComboFillDecomposer(cache, logger)
        self._combo_orders: Dict[ClientOrderId, OptionCombo] = {}
        
    def register_combo_order(self, order_id: ClientOrderId, combo: OptionCombo):
        """Register a combo order for tracking."""
        self._combo_orders[order_id] = combo
        
    def handle_combo_fill(self, combo_fill: OrderFilled) -> List[OrderFilled]:
        """
        Handle a combo fill and generate leg fills.
        
        Parameters
        ----------
        combo_fill : OrderFilled
            The combo fill event.
            
        Returns
        -------
        List[OrderFilled]
            List of leg fill events.
        """
        # Check if this is a combo order
        if combo_fill.client_order_id not in self._combo_orders:
            return []  # Not a combo order
            
        # Decompose into leg fills
        leg_fills = self._decomposer.decompose_combo_fill(combo_fill)
        
        self._logger.info(
            f"Processed combo fill {combo_fill.client_order_id}: "
            f"generated {len(leg_fills)} leg fills"
        )
        
        return leg_fills


class EnhancedComboExecutionHandler(ComboBacktestExecutionHandler):
    """
    Enhanced combo execution handler with sophisticated fill modeling.

    This handler extends the basic combo execution with:
    1. Realistic fill probability based on bid-ask positioning
    2. Partial fill modeling for large orders
    3. Time-based execution probability decay
    4. Market depth consideration
    """

    def __init__(
        self,
        cache: Cache,
        logger: Logger,
        fill_model: Optional[ComboFillModel] = None,
    ):
        super().__init__(cache, logger)
        self._fill_model = fill_model or ComboFillModel()
        self._pending_orders: Dict[ClientOrderId, Order] = {}

    def register_combo_order_with_model(self, order_id: ClientOrderId, combo: OptionCombo, order: Order):
        """Register a combo order with its order object for fill modeling."""
        super().register_combo_order(order_id, combo)
        self._pending_orders[order_id] = order

    def should_fill_combo_order(self, order: Order) -> tuple[bool, Optional[Quantity]]:
        """
        Determine if a combo order should be filled based on market conditions.

        Parameters
        ----------
        order : Order
            The combo order to evaluate.

        Returns
        -------
        tuple[bool, Optional[Quantity]]
            Whether to fill and the fill quantity (None for full fill).
        """
        combo_instrument = self._combo_orders.get(order.client_order_id)
        if combo_instrument is None:
            self._logger.warning(f"No combo instrument found for order {order.client_order_id}")
            return False, None

        return self._fill_model.is_combo_limit_filled(
            order, combo_instrument, self._cache
        )

    def handle_combo_fill(self, combo_fill: OrderFilled) -> List[OrderFilled]:
        """Handle combo fill with cleanup of fill model state."""
        leg_fills = super().handle_combo_fill(combo_fill)

        # Clean up fill model state
        self._fill_model.cleanup_order_state(combo_fill.client_order_id)
        self._pending_orders.pop(combo_fill.client_order_id, None)

        return leg_fills

    def get_fill_probability_info(self, order: Order) -> Optional[dict]:
        """
        Get detailed fill probability information for analysis.

        Parameters
        ----------
        order : Order
            The combo order.

        Returns
        -------
        dict, optional
            Fill probability details or None if not available.
        """
        combo_instrument = self._combo_orders.get(order.client_order_id)
        if combo_instrument is None:
            return None

        fill_prob, partial_ratio = self._fill_model.calculate_combo_fill_probability(
            order, combo_instrument, self._cache
        )

        return {
            "fill_probability": fill_prob,
            "partial_fill_ratio": partial_ratio,
            "order_id": str(order.client_order_id),
            "combo_id": str(combo_instrument.id),
        }
        
    def is_combo_order(self, order_id: ClientOrderId) -> bool:
        """Check if an order is a combo order."""
        return order_id in self._combo_orders
