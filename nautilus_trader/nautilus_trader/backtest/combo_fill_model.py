"""
Option combo fill model for realistic execution probability modeling.

This module provides a sophisticated fill model for option combo orders that considers:
- Position of limit order relative to bid-ask spread
- Market depth and liquidity
- Combo-specific execution dynamics
- Partial fill probabilities
"""

import math
import random
from datetime import datetime, time
from decimal import Decimal
from typing import Optional

from nautilus_trader.backtest.models import FillModel
from nautilus_trader.cache.cache import Cache
from nautilus_trader.core.correctness import PyCondition
from nautilus_trader.model.data import QuoteTick
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments import OptionCombo
from nautilus_trader.model.instruments.option_contract import OptionContract
from nautilus_trader.model.orders import Order
from nautilus_trader.model.objects import Price, Quantity


class ComboFillModel(FillModel):
    """
    Advanced fill model for option combo orders that provides realistic execution
    probability based on order position relative to market spreads.
    
    This model considers:
    - Individual leg bid-ask spreads and their impact on combo execution
    - Order aggressiveness (how far inside/outside the natural combo price)
    - Market depth simulation for partial fills
    - Time-based fill probability decay
    - Combo-specific liquidity characteristics
    
    Parameters
    ----------
    base_fill_prob : float, default 0.8
        Base probability for combo fills when order is at natural price.
    spread_sensitivity : float, default 2.0
        How sensitive fill probability is to spread position (higher = more sensitive).
    partial_fill_prob : float, default 0.3
        Probability of partial fills for large combo orders.
    min_fill_ratio : float, default 0.1
        Minimum fill ratio for partial fills.
    liquidity_factor : float, default 0.7
        Factor representing combo liquidity relative to individual legs.
    time_decay_factor : float, default 0.95
        Factor for time-based probability decay per market update.
    prob_slippage : float, default 0.1
        Probability of price slippage.
    random_seed : int, optional
        Random seed for reproducible results.
    """
    
    def __init__(
        self,
        base_fill_prob: float = 0.8,
        spread_sensitivity: float = 2.0,
        partial_fill_prob: float = 0.3,
        min_fill_ratio: float = 0.1,
        liquidity_factor: float = 0.7,
        time_decay_factor: float = 0.95,
        prob_slippage: float = 0.1,
        random_seed: Optional[int] = None,
        volatility_adjustment: bool = True,
        time_of_day_adjustment: bool = True,
        execution_profile: str = "retail",
    ):
        # Initialize base FillModel
        super().__init__(
            prob_fill_on_limit=base_fill_prob,
            prob_fill_on_stop=base_fill_prob,
            prob_slippage=prob_slippage,
            random_seed=random_seed,
        )
        
        # Validate combo-specific parameters
        PyCondition.in_range(base_fill_prob, 0.0, 1.0, "base_fill_prob")
        PyCondition.in_range(spread_sensitivity, 0.1, 10.0, "spread_sensitivity")
        PyCondition.in_range(partial_fill_prob, 0.0, 1.0, "partial_fill_prob")
        PyCondition.in_range(min_fill_ratio, 0.01, 1.0, "min_fill_ratio")
        PyCondition.in_range(liquidity_factor, 0.1, 1.0, "liquidity_factor")
        PyCondition.in_range(time_decay_factor, 0.5, 1.0, "time_decay_factor")
        
        self.base_fill_prob = base_fill_prob
        self.spread_sensitivity = spread_sensitivity
        self.partial_fill_prob = partial_fill_prob
        self.min_fill_ratio = min_fill_ratio
        self.liquidity_factor = liquidity_factor
        self.time_decay_factor = time_decay_factor
        self.volatility_adjustment = volatility_adjustment
        self.time_of_day_adjustment = time_of_day_adjustment
        self.execution_profile = execution_profile

        # Track order states for time decay
        self._order_states = {}

    def _calculate_volatility_adjustment(
        self,
        combo_instrument: OptionCombo,
        cache: Cache,
    ) -> float:
        """
        Calculate volatility-based adjustment factor.

        Parameters
        ----------
        combo_instrument : OptionCombo
            The combo instrument.
        cache : Cache
            The data cache.

        Returns
        -------
        float
            Volatility adjustment factor (0.5 to 2.0).
        """
        if not self.volatility_adjustment:
            return 1.0

        try:
            # Get implied volatilities from option legs
            total_vega = 0.0
            weighted_iv = 0.0

            for leg in combo_instrument.legs:
                instrument = cache.instrument(leg.instrument_id)
                if isinstance(instrument, OptionContract):
                    # Estimate implied volatility from option price and moneyness
                    quote = cache.quote_tick(leg.instrument_id)
                    if quote:
                        mid_price = (quote.bid_price + quote.ask_price) / 2
                        # Simple IV estimation based on moneyness and time to expiry
                        moneyness = float(instrument.strike_price) / 100.0  # Simplified
                        time_to_expiry = max(0.01, (instrument.expiration_ns - instrument.activation_ns) / (365.25 * 24 * 3600 * 1_000_000_000))

                        # Rough IV estimation (simplified Black-Scholes approximation)
                        estimated_iv = max(0.05, min(2.0, float(mid_price) / (100.0 * math.sqrt(time_to_expiry))))

                        # Weight by absolute ratio (vega proxy)
                        weight = abs(leg.ratio)
                        total_vega += weight
                        weighted_iv += estimated_iv * weight

            if total_vega > 0:
                avg_iv = weighted_iv / total_vega

                # High IV (>0.5) reduces fill probability (wider spreads, less liquidity)
                # Low IV (<0.2) increases fill probability (tighter spreads, more liquidity)
                if avg_iv > 0.5:
                    return max(0.5, 1.0 - (avg_iv - 0.5) * 0.8)  # Reduce by up to 40%
                elif avg_iv < 0.2:
                    return min(2.0, 1.0 + (0.2 - avg_iv) * 2.0)  # Increase by up to 40%
                else:
                    return 1.0

        except Exception:
            # Fallback if volatility calculation fails
            pass

        return 1.0

    def _calculate_time_of_day_adjustment(self, current_time: Optional[datetime] = None) -> float:
        """
        Calculate time-of-day liquidity adjustment.

        Parameters
        ----------
        current_time : datetime, optional
            Current market time. If None, uses current system time.

        Returns
        -------
        float
            Time-of-day adjustment factor (0.3 to 1.5).
        """
        if not self.time_of_day_adjustment:
            return 1.0

        if current_time is None:
            current_time = datetime.now()

        market_time = current_time.time()

        # Market open (9:30-10:30 ET): High volatility, moderate liquidity
        if time(9, 30) <= market_time <= time(10, 30):
            return 0.8
        # Mid-morning (10:30-11:30 ET): Good liquidity
        elif time(10, 30) <= market_time <= time(11, 30):
            return 1.2
        # Lunch hour (11:30-13:30 ET): Lower liquidity
        elif time(11, 30) <= market_time <= time(13, 30):
            return 0.6
        # Afternoon (13:30-15:00 ET): Good liquidity
        elif time(13, 30) <= market_time <= time(15, 0):
            return 1.1
        # Market close (15:00-16:00 ET): High volatility, variable liquidity
        elif time(15, 0) <= market_time <= time(16, 0):
            return 0.7
        # After hours: Very low liquidity
        else:
            return 0.3

    def _calculate_execution_profile_adjustment(self) -> float:
        """
        Calculate adjustment based on execution profile.

        Returns
        -------
        float
            Execution profile adjustment factor.
        """
        if self.execution_profile == "market_maker":
            # Market makers get better fills due to rebates and priority
            return 1.3
        else:  # retail
            # Retail orders face more adverse selection
            return 0.9

    def _calculate_microstructure_adjustment(
        self,
        combo_instrument: OptionCombo,
        cache: Cache,
    ) -> float:
        """
        Calculate adjustment based on market microstructure data.

        Parameters
        ----------
        combo_instrument : OptionCombo
            The combo instrument.
        cache : Cache
            The data cache.

        Returns
        -------
        float
            Microstructure adjustment factor (0.2 to 2.0).
        """
        try:
            total_spread_ratio = 0.0
            total_weight = 0.0

            for leg in combo_instrument.legs:
                quote = cache.quote_tick(leg.instrument_id)
                if quote:
                    # Calculate bid-ask spread ratio
                    mid_price = (quote.bid_price + quote.ask_price) / 2
                    spread = quote.ask_price - quote.bid_price
                    spread_ratio = float(spread) / float(mid_price) if mid_price > 0 else 0.1

                    # Weight by leg ratio
                    weight = abs(leg.ratio)
                    total_spread_ratio += spread_ratio * weight
                    total_weight += weight

            if total_weight > 0:
                avg_spread_ratio = total_spread_ratio / total_weight

                # Wide spreads (>5%) significantly reduce fill probability
                if avg_spread_ratio > 0.05:
                    return max(0.2, 1.0 - (avg_spread_ratio - 0.05) * 10.0)
                # Tight spreads (<1%) increase fill probability
                elif avg_spread_ratio < 0.01:
                    return min(2.0, 1.0 + (0.01 - avg_spread_ratio) * 50.0)
                else:
                    # Linear adjustment between 1% and 5%
                    return 1.0 - (avg_spread_ratio - 0.01) * 2.5

        except Exception:
            # Fallback if microstructure calculation fails
            pass

        return 1.0

    def calculate_combo_fill_probability(
        self,
        order: Order,
        combo_instrument: OptionCombo,
        cache: Cache,
    ) -> tuple[float, Optional[float]]:
        """
        Calculate fill probability and partial fill ratio for a combo order.
        
        Parameters
        ----------
        order : Order
            The combo order to evaluate.
        combo_instrument : OptionCombo
            The combo instrument.
        cache : Cache
            Market data cache.
            
        Returns
        -------
        tuple[float, Optional[float]]
            Fill probability and partial fill ratio (if applicable).
        """
        if not order.has_price:
            # Market orders have high fill probability
            return 0.95 * self.liquidity_factor, None
            
        order_price = order.price
        natural_combo_price = self._calculate_natural_combo_price(combo_instrument, cache)
        
        if natural_combo_price is None:
            # No market data available, use base probability
            return self.base_fill_prob * 0.5, None
            
        # Calculate order aggressiveness
        aggressiveness = self._calculate_order_aggressiveness(
            order, order_price, natural_combo_price
        )
        
        # Base probability adjusted by aggressiveness
        fill_prob = self._calculate_aggressiveness_adjusted_probability(aggressiveness)
        
        # Apply liquidity factor for combos
        fill_prob *= self.liquidity_factor

        # Apply volatility-based adjustment
        volatility_adj = self._calculate_volatility_adjustment(combo_instrument, cache)
        fill_prob *= volatility_adj

        # Apply time-of-day adjustment
        time_adj = self._calculate_time_of_day_adjustment()
        fill_prob *= time_adj

        # Apply execution profile adjustment
        profile_adj = self._calculate_execution_profile_adjustment()
        fill_prob *= profile_adj

        # Apply microstructure adjustment
        microstructure_adj = self._calculate_microstructure_adjustment(combo_instrument, cache)
        fill_prob *= microstructure_adj

        # Apply time decay if order has been resting
        fill_prob = self._apply_time_decay(order, fill_prob)
        
        # Determine partial fill ratio for large orders
        partial_fill_ratio = None
        if order.quantity.as_decimal() > Decimal('10') and random.random() < self.partial_fill_prob:
            partial_fill_ratio = max(
                self.min_fill_ratio,
                random.uniform(self.min_fill_ratio, 1.0)
            )
            
        return min(fill_prob, 1.0), partial_fill_ratio
        
    def _calculate_natural_combo_price(
        self,
        combo_instrument: OptionCombo,
        cache: Cache,
    ) -> Optional[Price]:
        """Calculate the natural (theoretical) price of the combo based on leg prices."""
        total_bid = Decimal('0')
        total_ask = Decimal('0')
        legs_with_quotes = 0
        
        for leg in combo_instrument.legs:
            quote = cache.quote_tick(leg.instrument_id)
            if quote is None:
                continue
                
            legs_with_quotes += 1
            leg_weight = Decimal(str(leg.ratio))
            
            if leg_weight > 0:  # Long leg
                total_bid += quote.bid_price.as_decimal() * leg_weight
                total_ask += quote.ask_price.as_decimal() * leg_weight
            else:  # Short leg
                total_bid += quote.ask_price.as_decimal() * abs(leg_weight)
                total_ask += quote.bid_price.as_decimal() * abs(leg_weight)
                
        if legs_with_quotes == 0:
            return None
            
        # Use mid price as natural price
        natural_price = (total_bid + total_ask) / Decimal('2')
        return Price(natural_price, precision=combo_instrument.price_precision)
        
    def _calculate_order_aggressiveness(
        self,
        order: Order,
        order_price: Price,
        natural_price: Price,
    ) -> float:
        """
        Calculate how aggressive the order is relative to natural price.
        
        Returns
        -------
        float
            Aggressiveness factor: 
            - Positive values = aggressive (better than natural price)
            - Negative values = passive (worse than natural price)
            - 0 = at natural price
        """
        price_diff = order_price.as_decimal() - natural_price.as_decimal()
        natural_price_value = natural_price.as_decimal()
        
        if natural_price_value == 0:
            return 0.0
            
        # Normalize by natural price to get relative aggressiveness
        relative_diff = float(price_diff / natural_price_value)
        
        # Adjust sign based on order side
        if order.side == OrderSide.BUY:
            # For buy orders, higher price = more aggressive
            return relative_diff
        else:
            # For sell orders, lower price = more aggressive
            return -relative_diff
            
    def _calculate_aggressiveness_adjusted_probability(self, aggressiveness: float) -> float:
        """Calculate fill probability based on order aggressiveness."""
        if aggressiveness > 0:
            # Aggressive orders (better than natural price) have higher fill probability
            # Use exponential function to reward aggressiveness
            multiplier = 1.0 + (aggressiveness * self.spread_sensitivity)
            return min(self.base_fill_prob * multiplier, 1.0)
        elif aggressiveness < 0:
            # Passive orders (worse than natural price) have lower fill probability
            # Use exponential decay
            multiplier = max(0.1, 1.0 + (aggressiveness * self.spread_sensitivity))
            return self.base_fill_prob * multiplier
        else:
            # At natural price
            return self.base_fill_prob
            
    def _apply_time_decay(self, order: Order, base_prob: float) -> float:
        """Apply time-based probability decay for resting orders."""
        order_id = order.client_order_id
        
        if order_id not in self._order_states:
            self._order_states[order_id] = {'updates': 0}
            
        # Increment update count (simulates time passing)
        self._order_states[order_id]['updates'] += 1
        updates = self._order_states[order_id]['updates']
        
        # Apply decay: probability decreases slightly with each market update
        decay_factor = self.time_decay_factor ** min(updates, 10)  # Cap decay
        return base_prob * decay_factor
        
    def is_combo_limit_filled(
        self,
        order: Order,
        combo_instrument: OptionCombo,
        cache: Cache,
    ) -> tuple[bool, Optional[Quantity]]:
        """
        Determine if a combo limit order should be filled.
        
        Parameters
        ----------
        order : Order
            The combo order.
        combo_instrument : OptionCombo
            The combo instrument.
        cache : Cache
            Market data cache.
            
        Returns
        -------
        tuple[bool, Optional[Quantity]]
            Whether order should fill and the fill quantity (if partial).
        """
        fill_prob, partial_ratio = self.calculate_combo_fill_probability(
            order, combo_instrument, cache
        )
        
        should_fill = random.random() < fill_prob
        
        if not should_fill:
            return False, None
            
        # Determine fill quantity
        if partial_ratio is not None:
            fill_qty = Quantity(
                order.leaves_qty.as_decimal() * Decimal(str(partial_ratio)),
                precision=order.quantity.precision,
            )
            return True, fill_qty
        else:
            return True, order.leaves_qty
            
    def cleanup_order_state(self, order_id):
        """Clean up tracking state for completed orders."""
        self._order_states.pop(order_id, None)
        
    def __repr__(self) -> str:
        return (
            f"ComboFillModel("
            f"base_fill_prob={self.base_fill_prob}, "
            f"spread_sensitivity={self.spread_sensitivity}, "
            f"partial_fill_prob={self.partial_fill_prob}, "
            f"liquidity_factor={self.liquidity_factor})"
        )
