#!/usr/bin/env python3

"""
Option combo execution engine for coordinating multi-leg order execution.
"""

from typing import Any

from nautilus_trader.cache.cache import Cache
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.core.correctness import PyCondition
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.execution.messages import SubmitOrder
from nautilus_trader.execution.messages import TradingCommand
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.events import OrderFilled
from nautilus_trader.model.identifiers import AccountId
from nautilus_trader.model.identifiers import ClientOrderId
from nautilus_trader.model.identifiers import StrategyId
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.model.identifiers import VenueOrderId
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.objects import Money
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity
from nautilus_trader.model.orders import Order
from nautilus_trader.msgbus.bus import MessageBus


class ComboOrderExecution:
    """
    Manages the execution of option combo orders by coordinating individual leg orders.

    This class handles:
    1. Decomposing combo orders into individual leg orders
    2. Coordinating execution across multiple legs
    3. Converting individual leg fills back into combo-level reporting
    4. Managing partial fills and order state consistency

    """

    def __init__(
        self,
        trader_id: TraderId,
        cache: Cache,
        msgbus: MessageBus,
        logger: Logger,
        clock: LiveClock,
    ) -> None:
        """
        Initialize the combo order execution engine.

        Parameters
        ----------
        trader_id : TraderId
            The trader ID for the execution engine.
        cache : Cache
            The cache for accessing instruments and orders.
        msgbus : MessageBus
            The message bus for publishing events.
        logger : Logger
            The logger for the execution engine.
        clock : LiveClock
            The clock for timestamping.

        """
        self._trader_id = trader_id
        self._cache = cache
        self._msgbus = msgbus
        self._logger = logger
        self._clock = clock

        # Track combo orders and their component legs
        self._combo_orders: dict[ClientOrderId, OptionCombo] = {}
        self._combo_leg_orders: dict[ClientOrderId, list[ClientOrderId]] = {}
        self._leg_to_combo: dict[ClientOrderId, ClientOrderId] = {}
        self._combo_fills: dict[ClientOrderId, list[OrderFilled]] = {}

    def register_combo_order(
        self,
        combo_order_id: ClientOrderId,
        combo: OptionCombo,
        leg_orders: list[Order],
    ) -> None:
        """
        Register a combo order and its component leg orders.

        Parameters
        ----------
        combo_order_id : ClientOrderId
            The client order ID for the combo order.
        combo : OptionCombo
            The option combo instrument.
        leg_orders : list[Order]
            The individual leg orders.

        """
        PyCondition.not_none(combo_order_id, "combo_order_id")
        PyCondition.not_none(combo, "combo")
        PyCondition.not_empty(leg_orders, "leg_orders")

        # Store combo information
        self._combo_orders[combo_order_id] = combo
        leg_order_ids = [order.client_order_id for order in leg_orders]
        self._combo_leg_orders[combo_order_id] = leg_order_ids
        self._combo_fills[combo_order_id] = []

        # Create reverse mapping from leg to combo
        for leg_order_id in leg_order_ids:
            self._leg_to_combo[leg_order_id] = combo_order_id

        self._logger.info(
            f"Registered combo order {combo_order_id} with {len(leg_orders)} legs",
        )

    def submit_combo_orders(
        self,
        combo_order_id: ClientOrderId,
        leg_orders: list[Order],
        strategy_id: StrategyId,
        account_id: AccountId,
    ) -> list[TradingCommand]:
        """
        Submit individual leg orders for a combo.

        Parameters
        ----------
        combo_order_id : ClientOrderId
            The combo order ID.
        leg_orders : list[Order]
            The individual leg orders to submit.
        strategy_id : StrategyId
            The strategy ID.
        account_id : AccountId
            The account ID.

        Returns
        -------
        list[TradingCommand]
            The submit order commands for each leg.

        """
        commands = []

        for order in leg_orders:
            command = SubmitOrder(
                trader_id=self._trader_id,
                strategy_id=strategy_id,
                order=order,
                command_id=UUID4(),
                ts_init=self._clock.timestamp_ns(),
            )
            commands.append(command)

        self._logger.info(
            f"Created {len(commands)} submit commands for combo {combo_order_id}",
        )

        return commands

    def handle_leg_fill(self, fill_event: OrderFilled) -> OrderFilled | None:
        """
        Handle a fill event for a leg order and potentially generate combo fill.

        Parameters
        ----------
        fill_event : OrderFilled
            The fill event for a leg order.

        Returns
        -------
        OrderFilled or None
            A synthetic combo fill event if all legs are filled, None otherwise.

        """
        leg_order_id = fill_event.client_order_id

        # Check if this is a leg of a combo order
        combo_order_id = self._leg_to_combo.get(leg_order_id)
        if combo_order_id is None:
            # Not a combo leg, return None
            return None

        # Store the fill for this combo
        self._combo_fills[combo_order_id].append(fill_event)

        # Check if all legs are filled
        leg_order_ids = self._combo_leg_orders[combo_order_id]
        filled_legs = {fill.client_order_id for fill in self._combo_fills[combo_order_id]}

        if len(filled_legs) == len(leg_order_ids):
            # All legs filled, generate combo fill
            return self._generate_combo_fill(combo_order_id)

        self._logger.debug(
            f"Combo {combo_order_id}: {len(filled_legs)}/{len(leg_order_ids)} legs filled",
        )

        return None

    def _generate_combo_fill(self, combo_order_id: ClientOrderId) -> OrderFilled:
        """
        Generate a synthetic combo fill event from individual leg fills.

        Parameters
        ----------
        combo_order_id : ClientOrderId
            The combo order ID.

        Returns
        -------
        OrderFilled
            The synthetic combo fill event.

        """
        combo = self._combo_orders[combo_order_id]
        leg_fills = self._combo_fills[combo_order_id]

        # Calculate combo fill details
        combo_price = self._calculate_combo_fill_price(combo, leg_fills)
        combo_quantity = self._calculate_combo_fill_quantity(leg_fills)
        combo_commission = self._calculate_combo_commission(leg_fills)

        # Use details from first leg fill for common fields
        first_fill = leg_fills[0]

        # Create synthetic combo fill event
        combo_fill = OrderFilled(
            trader_id=first_fill.trader_id,
            strategy_id=first_fill.strategy_id,
            instrument_id=combo.id,  # Use combo instrument ID
            client_order_id=combo_order_id,
            venue_order_id=VenueOrderId(f"COMBO-{combo_order_id.value}"),
            account_id=first_fill.account_id,
            trade_id=first_fill.trade_id,  # Could generate unique combo trade ID
            order_side=self._determine_combo_side(combo, leg_fills),
            order_type=first_fill.order_type,
            last_qty=combo_quantity,
            last_px=combo_price,
            currency=first_fill.currency,
            commission=combo_commission,
            liquidity_side=first_fill.liquidity_side,
            event_id=UUID4(),
            ts_event=max(fill.ts_event for fill in leg_fills),
            ts_init=self._clock.timestamp_ns(),
        )

        self._logger.info(
            f"Generated combo fill for {combo_order_id}: "
            f"price={combo_price}, qty={combo_quantity}, commission={combo_commission}",
        )

        return combo_fill

    def _calculate_combo_fill_price(
        self,
        combo: OptionCombo,
        leg_fills: list[OrderFilled],
    ) -> Price:
        """
        Calculate the effective combo fill price from leg fills.

        Parameters
        ----------
        combo : OptionCombo
            The option combo.
        leg_fills : list[OrderFilled]
            The individual leg fill events.

        Returns
        -------
        Price
            The effective combo fill price.

        """
        # Create price mapping from fills
        leg_prices = {}
        for fill in leg_fills:
            leg_prices[fill.instrument_id] = fill.last_px

        # Calculate combo price using the combo's pricing logic
        combo_price = combo.calculate_combo_price(leg_prices)

        return combo_price

    def _calculate_combo_fill_quantity(self, leg_fills: list[OrderFilled]) -> Quantity:
        """
        Calculate the combo fill quantity (should be consistent across legs).

        Parameters
        ----------
        leg_fills : list[OrderFilled]
            The individual leg fill events.

        Returns
        -------
        Quantity
            The combo fill quantity.

        """
        # For option combos, all legs should have the same quantity
        # (adjusted for the leg ratio, but the combo quantity is the base unit)
        quantities = [fill.last_qty for fill in leg_fills]

        # Use the minimum quantity as the combo quantity (conservative approach)
        min_qty = min(quantities)

        return min_qty

    def _calculate_combo_commission(self, leg_fills: list[OrderFilled]) -> Money:
        """
        Calculate the total combo commission from leg commissions.

        Parameters
        ----------
        leg_fills : list[OrderFilled]
            The individual leg fill events.

        Returns
        -------
        Money
            The total combo commission.

        """
        # Sum all leg commissions
        total_commission = sum(fill.commission.as_decimal() for fill in leg_fills)

        # Use currency from first fill
        currency = leg_fills[0].commission.currency

        return Money(total_commission, currency)

    def _determine_combo_side(
        self,
        combo: OptionCombo,
        leg_fills: list[OrderFilled],
    ) -> OrderSide:
        """
        Determine the effective combo order side.

        Parameters
        ----------
        combo : OptionCombo
            The option combo.
        leg_fills : list[OrderFilled]
            The individual leg fill events.

        Returns
        -------
        OrderSide
            The effective combo order side.

        """
        # For combos, the side is determined by the net effect
        # This is a simplified approach - could be more sophisticated
        buy_legs = sum(1 for fill in leg_fills if fill.order_side == OrderSide.BUY)
        sell_legs = len(leg_fills) - buy_legs

        return OrderSide.BUY if buy_legs >= sell_legs else OrderSide.SELL

    def get_combo_status(self, combo_order_id: ClientOrderId) -> dict[str, Any]:
        """
        Get the current status of a combo order.

        Parameters
        ----------
        combo_order_id : ClientOrderId
            The combo order ID.

        Returns
        -------
        dict[str, Any]
            Status information for the combo order.

        """
        if combo_order_id not in self._combo_orders:
            return {"error": "Combo order not found"}

        leg_order_ids = self._combo_leg_orders[combo_order_id]
        filled_legs = {fill.client_order_id for fill in self._combo_fills[combo_order_id]}

        return {
            "combo_id": combo_order_id,
            "total_legs": len(leg_order_ids),
            "filled_legs": len(filled_legs),
            "is_complete": len(filled_legs) == len(leg_order_ids),
            "leg_fills": len(self._combo_fills[combo_order_id]),
        }
