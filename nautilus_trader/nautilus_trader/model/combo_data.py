#!/usr/bin/env python3

"""
Option combo market data provider and synthetic quote generation.
"""


from nautilus_trader.cache.cache import Cache
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.core.correctness import PyCondition
from nautilus_trader.model.data import QuoteTick
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.objects import Price


class ComboMarketDataProvider:
    """
    Provides synthetic market data for option combos based on component leg quotes.

    This class generates synthetic bid/ask quotes for option combos by:
    1. Collecting quotes from all component legs
    2. Calculating combo mid price using linear combination
    3. Applying vega-weighted spread calculation
    4. Publishing synthetic QuoteTick for the combo

    """

    def __init__(
        self,
        cache: Cache,
        logger: Logger,
        clock: LiveClock,
    ) -> None:
        """
        Initialize the combo market data provider.

        Parameters
        ----------
        cache : Cache
            The cache for accessing market data and instruments.
        logger : Logger
            The logger for the provider.
        clock : LiveClock
            The clock for timestamping.

        """
        self._cache = cache
        self._logger = logger
        self._clock = clock

        # Track combo subscriptions and leg data
        self._combo_subscriptions: dict[InstrumentId, OptionCombo] = {}
        self._leg_quotes: dict[InstrumentId, QuoteTick] = {}
        self._combo_quotes: dict[InstrumentId, QuoteTick] = {}

    def subscribe_combo_quotes(self, combo: OptionCombo) -> None:
        """
        Subscribe to synthetic quotes for an option combo.

        Parameters
        ----------
        combo : OptionCombo
            The option combo to subscribe to.

        """
        PyCondition.not_none(combo, "combo")

        self._combo_subscriptions[combo.id] = combo
        self._logger.info(f"Subscribed to combo quotes for {combo.id}")

        # Subscribe to all leg quotes
        for leg in combo.legs:
            self._logger.info(f"Subscribing to leg quotes for {leg.instrument_id}")

    def unsubscribe_combo_quotes(self, combo_id: InstrumentId) -> None:
        """
        Unsubscribe from synthetic quotes for an option combo.

        Parameters
        ----------
        combo_id : InstrumentId
            The combo instrument ID to unsubscribe from.

        """
        if combo_id in self._combo_subscriptions:
            del self._combo_subscriptions[combo_id]
            self._logger.info(f"Unsubscribed from combo quotes for {combo_id}")

    def on_quote_tick(self, tick: QuoteTick) -> QuoteTick | None:
        """
        Handle incoming quote tick for a component leg.

        This method is called when a quote tick is received for any instrument.
        If the instrument is a component of a subscribed combo, it will trigger
        synthetic quote generation for that combo.

        Parameters
        ----------
        tick : QuoteTick
            The quote tick for a component instrument.

        Returns
        -------
        QuoteTick or None
            The synthetic combo quote tick if generated, None otherwise.

        """
        # Store the leg quote
        self._leg_quotes[tick.instrument_id] = tick

        # Check if this leg is part of any subscribed combos
        for combo_id, combo in self._combo_subscriptions.items():
            leg_instrument_ids = {leg.instrument_id for leg in combo.legs}

            if tick.instrument_id in leg_instrument_ids:
                # Generate synthetic quote for this combo
                synthetic_quote = self._generate_combo_quote(combo)
                if synthetic_quote:
                    self._combo_quotes[combo_id] = synthetic_quote
                    return synthetic_quote

        return None

    def _generate_combo_quote(self, combo: OptionCombo) -> QuoteTick | None:
        """
        Generate a synthetic quote for an option combo.

        Parameters
        ----------
        combo : OptionCombo
            The option combo to generate a quote for.

        Returns
        -------
        QuoteTick or None
            The synthetic quote tick, or None if not enough data.

        """
        # Collect current quotes for all legs
        leg_bid_prices = {}
        leg_ask_prices = {}
        leg_bid_sizes = {}
        leg_ask_sizes = {}

        for leg in combo.legs:
            quote = self._leg_quotes.get(leg.instrument_id)
            if quote is None:
                # Not enough data to generate combo quote
                return None

            leg_bid_prices[leg.instrument_id] = quote.bid_price
            leg_ask_prices[leg.instrument_id] = quote.ask_price
            leg_bid_sizes[leg.instrument_id] = quote.bid_size
            leg_ask_sizes[leg.instrument_id] = quote.ask_size

        # Calculate combo bid price (worst case for buying the combo)
        combo_bid_price = combo.calculate_combo_price(leg_bid_prices)

        # Calculate combo ask price (worst case for selling the combo)
        combo_ask_price = combo.calculate_combo_price(leg_ask_prices)

        # For size, use minimum leg size (conservative approach)
        min_bid_size = min(leg_bid_sizes.values())
        min_ask_size = min(leg_ask_sizes.values())

        # Create synthetic quote tick
        ts_event = max(
            quote.ts_event
            for quote in self._leg_quotes.values()
            if quote.instrument_id in {leg.instrument_id for leg in combo.legs}
        )

        synthetic_quote = QuoteTick(
            instrument_id=combo.id,
            bid_price=combo_bid_price,
            ask_price=combo_ask_price,
            bid_size=min_bid_size,
            ask_size=min_ask_size,
            ts_event=ts_event,
            ts_init=self._clock.timestamp_ns(),
        )

        self._logger.debug(
            f"Generated synthetic quote for {combo.id}: "
            f"bid={combo_bid_price} ask={combo_ask_price}",
        )

        return synthetic_quote

    def get_combo_quote(self, combo_id: InstrumentId) -> QuoteTick | None:
        """
        Get the latest synthetic quote for a combo.

        Parameters
        ----------
        combo_id : InstrumentId
            The combo instrument ID.

        Returns
        -------
        QuoteTick or None
            The latest synthetic quote, or None if not available.

        """
        return self._combo_quotes.get(combo_id)

    def get_combo_mid_price(self, combo_id: InstrumentId) -> Price | None:
        """
        Get the mid price for a combo.

        Parameters
        ----------
        combo_id : InstrumentId
            The combo instrument ID.

        Returns
        -------
        Price or None
            The mid price, or None if not available.

        """
        quote = self._combo_quotes.get(combo_id)
        if quote is None:
            return None

        mid_price = (quote.bid_price.as_f64() + quote.ask_price.as_f64()) / 2.0
        return Price.from_str(f"{mid_price:.{quote.bid_price.precision}f}")

    def is_combo_quote_available(self, combo_id: InstrumentId) -> bool:
        """
        Check if a synthetic quote is available for a combo.

        Parameters
        ----------
        combo_id : InstrumentId
            The combo instrument ID.

        Returns
        -------
        bool
            True if quote is available, False otherwise.

        """
        return combo_id in self._combo_quotes
