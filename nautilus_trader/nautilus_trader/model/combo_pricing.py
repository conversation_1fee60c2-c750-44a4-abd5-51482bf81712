# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Option combo pricing engine that integrates with GreeksCalculator.
"""


from nautilus_trader.cache.base import CacheFacade
from nautilus_trader.common.component import LiveClock
from nautilus_trader.common.component import Logger
from nautilus_trader.common.component import MessageBus
from nautilus_trader.core.data import Data
from nautilus_trader.model.greeks import GreeksCalculator
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity


class ComboQuote(Data):
    """
    Represents a synthetic quote for an option combo.

    Parameters
    ----------
    instrument_id : InstrumentId
        The combo instrument ID.
    bid_price : Price
        The calculated bid price.
    ask_price : Price
        The calculated ask price.
    bid_size : Quantity
        The calculated bid size.
    ask_size : Quantity
        The calculated ask size.
    ts_event : int
        The UNIX timestamp (nanoseconds) when the quote was generated.
    ts_init : int
        The UNIX timestamp (nanoseconds) when the object was initialized.

    """

    def __init__(
        self,
        instrument_id: InstrumentId,
        bid_price: Price,
        ask_price: Price,
        bid_size: Quantity,
        ask_size: Quantity,
        ts_event: int,
        ts_init: int,
    ) -> None:
        super().__init__(ts_event=ts_event, ts_init=ts_init)
        self.instrument_id = instrument_id
        self.bid_price = bid_price
        self.ask_price = ask_price
        self.bid_size = bid_size
        self.ask_size = ask_size

    def __repr__(self) -> str:
        return (
            f"{type(self).__name__}("
            f"instrument_id={self.instrument_id}, "
            f"bid={self.bid_price}@{self.bid_size}, "
            f"ask={self.ask_price}@{self.ask_size})"
        )


class ComboPricingEngine:
    """
    Pricing engine for option combos that calculates synthetic quotes.

    Uses the GreeksCalculator to obtain individual leg prices and Greeks,
    then calculates combo prices as linear combinations and sizes based
    on vega-weighted calculations.

    Parameters
    ----------
    msgbus : MessageBus
        The message bus for the engine.
    cache : CacheFacade
        The cache for the engine.
    clock : LiveClock
        The clock for the engine.
    logger : Logger
        The logger for the engine.
    vega_multiplier : float, default 1.0
        The vega multiplier for spread calculations. Scales the combo's vega sensitivity for bid/ask spread.

    """

    def __init__(
        self,
        msgbus: MessageBus,
        cache: CacheFacade,
        clock: LiveClock,
        logger: Logger,
        vega_multiplier: float = 1.0,
    ) -> None:
        self._msgbus = msgbus
        self._cache = cache
        self._clock = clock
        self._logger = logger
        self._vega_multiplier = vega_multiplier

        # Initialize Greeks calculator
        self._greeks_calculator = GreeksCalculator(
            msgbus=msgbus,
            cache=cache,
            clock=clock,
        )

        # Track subscribed combos
        self._subscribed_combos: dict[InstrumentId, OptionCombo] = {}

        self._logger.info(f"ComboPricingEngine initialized with vega_multiplier={vega_multiplier}")

    def subscribe_combo(self, combo: OptionCombo) -> None:
        """
        Subscribe to pricing updates for an option combo.

        Parameters
        ----------
        combo : OptionCombo
            The combo instrument to subscribe to.

        """
        self._subscribed_combos[combo.id] = combo
        self._logger.info(f"Subscribed to combo pricing: {combo.id}")

    def unsubscribe_combo(self, instrument_id: InstrumentId) -> None:
        """
        Unsubscribe from pricing updates for an option combo.

        Parameters
        ----------
        instrument_id : InstrumentId
            The combo instrument ID to unsubscribe from.

        """
        if instrument_id in self._subscribed_combos:
            del self._subscribed_combos[instrument_id]
            self._logger.info(f"Unsubscribed from combo pricing: {instrument_id}")

    def calculate_combo_quote(
        self,
        combo: OptionCombo,
        base_spread_pct: float = 0.01,
    ) -> ComboQuote | None:
        """
        Calculate a synthetic quote for an option combo.

        Parameters
        ----------
        combo : OptionCombo
            The combo instrument.
        flat_interest_rate : float, default 0.0425
            The flat interest rate for Greeks calculations.
        base_spread_pct : float, default 0.01
            Base spread percentage for bid/ask calculation.

        Returns
        -------
        ComboQuote or None
            The calculated combo quote, or None if calculation fails.

        """
        try:
            # Get current timestamp
            ts_now = self._clock.timestamp_ns()

            # Calculate leg prices and Greeks
            leg_data = {}
            total_vega = 0.0
            combo_mid_price = 0.0
            reference_leg_instrument = (
                None  # Will use first leg's properties for rounding and sizing
            )

            for leg in combo.legs:
                # Get the leg instrument for properties (use first leg as reference)
                if reference_leg_instrument is None:
                    reference_leg_instrument = self._cache.instrument(leg.instrument_id)
                    if reference_leg_instrument is None:
                        self._logger.warning(
                            f"Leg instrument {leg.instrument_id} not found in cache",
                        )
                        return None

                # Get Greeks for this leg
                try:
                    greeks = self._greeks_calculator.instrument_greeks(
                        instrument_id=leg.instrument_id,
                        percent_greeks=True,  # Use percentage-based Greeks for spread calculation
                    )

                    leg_data[leg.instrument_id] = {
                        "greeks": greeks,
                        "price": greeks.price,
                        "vega": greeks.vega,
                        "ratio": leg.ratio,
                    }

                    # Calculate combo mid price as linear combination
                    combo_mid_price += leg.ratio * greeks.price

                    # Use percentage-based vega from Greeks calculation for spread calculation
                    # With percent_greeks=True, vega represents percentage price sensitivity to volatility changes
                    leg_vega_contribution = abs(leg.ratio * greeks.vega)
                    total_vega += leg_vega_contribution

                except Exception as e:
                    self._logger.warning(
                        f"Failed to calculate Greeks for leg {leg.instrument_id}: {e}",
                    )
                    return None

            if not leg_data:
                return None

            # Calculate vega-weighted spread using percentage-based Greeks
            # total_vega represents the combo's percentage sensitivity to volatility changes
            # vega_multiplier scales this sensitivity for spread calculation
            vega_spread_multiplier = 1.0 + (total_vega * self._vega_multiplier)
            spread = base_spread_pct * vega_spread_multiplier * combo_mid_price

            # Calculate bid/ask prices using reference leg's price precision
            if reference_leg_instrument is None:
                self._logger.error("Reference leg instrument is None")
                return None

            price_precision = reference_leg_instrument.price_precision
            bid_price = Price.from_str(f"{combo_mid_price - spread/2:.{price_precision}f}")
            ask_price = Price.from_str(f"{combo_mid_price + spread/2:.{price_precision}f}")

            # Calculate sizes based on vega and reference leg's minimum size
            leg_min_size = int(reference_leg_instrument.min_quantity)
            leg_max_size = (
                int(reference_leg_instrument.max_quantity)
                if reference_leg_instrument.max_quantity
                else 1000  # Default max size if not specified
            )

            vega_size_factor = max(0.1, min(1.0, 1.0 / (1.0 + total_vega * 0.1)))
            calculated_size = int(leg_max_size * vega_size_factor)
            quote_size = max(leg_min_size, min(calculated_size, leg_max_size))

            bid_size = Quantity.from_int(quote_size)
            ask_size = Quantity.from_int(quote_size)

            return ComboQuote(
                instrument_id=combo.id,
                bid_price=bid_price,
                ask_price=ask_price,
                bid_size=bid_size,
                ask_size=ask_size,
                ts_event=ts_now,
                ts_init=ts_now,
            )

        except Exception as e:
            self._logger.error(f"Failed to calculate combo quote for {combo.id}: {e}")
            return None

    def update_combo_quotes(self) -> None:
        """
        Update quotes for all subscribed combos.

        This method should be called when underlying market data changes.

        """
        for combo in self._subscribed_combos.values():
            quote = self.calculate_combo_quote(combo)
            if quote:
                # Publish the synthetic quote
                self._msgbus.publish(topic=f"data.quotes.{combo.id}", msg=quote)

    def get_combo_greeks(
        self,
        combo: OptionCombo,
        flat_interest_rate: float = 0.0425,
        percent_greeks: bool = False,
    ) -> dict[str, float] | None:
        """
        Calculate aggregated Greeks for an option combo.

        Parameters
        ----------
        combo : OptionCombo
            The combo instrument.
        flat_interest_rate : float, default 0.0425
            The flat interest rate for calculations.
        percent_greeks : bool, default False
            Whether to use percentage-based Greeks calculations.

        Returns
        -------
        dict[str, float] or None
            Dictionary containing aggregated Greeks, or None if calculation fails.

        """
        try:
            total_delta = 0.0
            total_gamma = 0.0
            total_vega = 0.0
            total_theta = 0.0

            for leg in combo.legs:
                try:
                    greeks = self._greeks_calculator.instrument_greeks(
                        instrument_id=leg.instrument_id,
                        flat_interest_rate=flat_interest_rate,
                        use_cached_greeks=False,
                        percent_greeks=percent_greeks,  # Use percentage-based Greeks for consistency
                    )

                    # Aggregate Greeks with leg ratios
                    total_delta += leg.ratio * greeks.delta
                    total_gamma += leg.ratio * greeks.gamma
                    total_vega += leg.ratio * greeks.vega
                    total_theta += leg.ratio * greeks.theta

                except Exception as e:
                    self._logger.warning(
                        f"Failed to get Greeks for leg {leg.instrument_id}: {e}",
                    )
                    return None

            return {
                "delta": total_delta,
                "gamma": total_gamma,
                "vega": total_vega,
                "theta": total_theta,
            }

        except Exception as e:
            self._logger.error(f"Failed to calculate combo Greeks for {combo.id}: {e}")
            return None
