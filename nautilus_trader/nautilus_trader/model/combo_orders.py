# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------
"""
Option combo order management and decomposition.
"""

from typing import Any

from nautilus_trader.cache.base import CacheFacade
from nautilus_trader.common.component import Clock
from nautilus_trader.common.component import Logger
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.enums import PriceType
from nautilus_trader.model.enums import TimeInForce
from nautilus_trader.model.identifiers import ClientOrderId
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import OrderListId
from nautilus_trader.model.identifiers import StrategyId
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.model.instruments.option_combo import ComboLeg
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity
from nautilus_trader.model.orders import LimitOrder
from nautilus_trader.model.orders import MarketOrder


class ComboOrderManager:
    """
    Manages option combo orders by decomposing them into individual leg orders.

    This class handles the conversion of combo orders into coordinated leg orders
    that can be executed individually while maintaining the combo relationship.

    Parameters
    ----------
    cache : CacheFacade
        The cache for retrieving instruments and market data.
    clock : Clock
        The clock for timestamps.
    logger : Logger
        The logger for the manager.

    """

    def __init__(
        self,
        cache: CacheFacade,
        clock: Clock,
        logger: Logger,
    ) -> None:
        self._cache = cache
        self._clock = clock
        self._logger = logger

        # Track combo orders and their leg orders
        self._combo_orders: dict[OrderListId, dict[str, Any]] = {}
        self._leg_orders: dict[ClientOrderId, OrderListId] = {}  # leg_order_id -> combo_order_id

    def create_combo_market_order(
        self,
        combo: OptionCombo,
        side: OrderSide,
        quantity: Quantity,
        trader_id: TraderId,
        strategy_id: StrategyId,
        time_in_force: TimeInForce = TimeInForce.GTC,
        tags: list[str] | None = None,
    ) -> list[MarketOrder]:
        """
        Create a market order list for an option combo.

        Parameters
        ----------
        combo : OptionCombo
            The combo instrument.
        side : OrderSide
            The order side (BUY or SELL).
        quantity : Quantity
            The combo quantity.
        trader_id : TraderId
            The trader ID.
        strategy_id : StrategyId
            The strategy ID.
        time_in_force : TimeInForce, default GTC
            The time in force.
        tags : list[str], optional
            Order tags.

        Returns
        -------
        list[MarketOrder]
            The list of market orders for each leg.

        """
        combo_order_id = OrderListId(str(UUID4()))
        leg_orders = []

        for leg in combo.legs:
            # Calculate leg side and quantity
            leg_side = self._calculate_leg_side(side, leg)
            leg_quantity = Quantity.from_int(
                int(quantity) * abs(leg.weight),
            )  # Use absolute weight for quantity

            # Create leg order
            leg_order_id = ClientOrderId(str(UUID4()))
            leg_order = MarketOrder(
                trader_id=trader_id,
                strategy_id=strategy_id,
                instrument_id=leg.instrument_id,
                client_order_id=leg_order_id,
                order_side=leg_side,
                quantity=leg_quantity,
                time_in_force=time_in_force,
                init_id=UUID4(),
                ts_init=self._clock.timestamp_ns(),
                tags=tags,
            )

            leg_orders.append(leg_order)
            self._leg_orders[leg_order_id] = combo_order_id

        # Track combo order
        self._combo_orders[combo_order_id] = {
            "combo": combo,
            "original_side": side,
            "original_quantity": quantity,
            "leg_orders": {order.client_order_id: order for order in leg_orders},
        }

        self._logger.info(
            f"Created combo market order: {combo_order_id} for {combo.id} "
            f"{side.name} {quantity} -> {len(leg_orders)} leg orders",
        )

        return leg_orders

    def create_combo_limit_order(
        self,
        combo: OptionCombo,
        side: OrderSide,
        quantity: Quantity,
        price: Price,
        trader_id: TraderId,
        strategy_id: StrategyId,
        time_in_force: TimeInForce = TimeInForce.GTC,
        leg_prices: dict[InstrumentId, Price] | None = None,
        tags: list[str] | None = None,
    ) -> list[LimitOrder]:
        """
        Create a limit order list for an option combo.

        Parameters
        ----------
        combo : OptionCombo
            The combo instrument.
        side : OrderSide
            The order side (BUY or SELL).
        quantity : Quantity
            The combo quantity.
        price : Price
            The combo limit price.
        trader_id : TraderId
            The trader ID.
        strategy_id : StrategyId
            The strategy ID.
        time_in_force : TimeInForce, default GTC
            The time in force.
        leg_prices : dict[InstrumentId, Price], optional
            Specific prices for each leg. If not provided, prices will be
            calculated proportionally from the combo price.
        tags : list[str], optional
            Order tags.

        Returns
        -------
        list[LimitOrder]
            The list of limit orders for each leg.

        """
        combo_order_id = OrderListId(str(UUID4()))
        leg_orders = []

        # Calculate leg prices if not provided
        if leg_prices is None:
            leg_prices = self._calculate_leg_prices(combo, price)

        for leg in combo.legs:
            # Calculate leg side and quantity
            leg_side = self._calculate_leg_side(side, leg)
            leg_quantity = Quantity.from_int(
                int(quantity) * abs(leg.weight),
            )  # Use absolute weight for quantity
            leg_price = leg_prices.get(leg.instrument_id)

            if leg_price is None:
                raise ValueError(f"No price provided for leg {leg.instrument_id}")

            # Create leg order
            leg_order_id = ClientOrderId(str(UUID4()))
            leg_order = LimitOrder(
                trader_id=trader_id,
                strategy_id=strategy_id,
                instrument_id=leg.instrument_id,
                client_order_id=leg_order_id,
                order_side=leg_side,
                quantity=leg_quantity,
                price=leg_price,
                time_in_force=time_in_force,
                init_id=UUID4(),
                ts_init=self._clock.timestamp_ns(),
                tags=tags,
            )

            leg_orders.append(leg_order)
            self._leg_orders[leg_order_id] = combo_order_id

        # Track combo order
        self._combo_orders[combo_order_id] = {
            "combo": combo,
            "original_side": side,
            "original_quantity": quantity,
            "original_price": price,
            "leg_orders": {order.client_order_id: order for order in leg_orders},
            "leg_prices": leg_prices,
        }

        self._logger.info(
            f"Created combo limit order: {combo_order_id} for {combo.id} "
            f"{side.name} {quantity} @ {price} -> {len(leg_orders)} leg orders",
        )

        return leg_orders

    def _calculate_leg_side(self, combo_side: OrderSide, leg: ComboLeg) -> OrderSide:
        """
        Calculate the order side for a leg based on combo side and leg weight.

        Parameters
        ----------
        combo_side : OrderSide
            The combo order side.
        leg : ComboLeg
            The combo leg.

        Returns
        -------
        OrderSide
            The calculated leg order side.

        """
        # Determine leg's natural side from weight sign
        leg_natural_side = OrderSide.BUY if leg.weight > 0 else OrderSide.SELL

        if combo_side == OrderSide.BUY:
            return leg_natural_side
        else:  # combo_side == OrderSide.SELL
            return OrderSide.SELL if leg_natural_side == OrderSide.BUY else OrderSide.BUY

    def _calculate_leg_prices(
        self,
        combo: OptionCombo,
        combo_price: Price,
    ) -> dict[InstrumentId, Price]:
        """
        Calculate individual leg prices from combo price.

        This is a simplified implementation that distributes the combo price
        proportionally based on leg weights. In practice, you might want to
        use current market prices and adjust them to achieve the target combo price.

        Parameters
        ----------
        combo : OptionCombo
            The combo instrument.
        combo_price : Price
            The target combo price.

        Returns
        -------
        dict[InstrumentId, Price]
            Dictionary mapping leg instrument IDs to their calculated prices.

        """
        leg_prices = {}
        combo_price_float = float(combo_price)

        # Get current market prices for legs
        total_weighted_price = 0.0
        leg_market_prices = {}

        for leg in combo.legs:
            market_price = self._cache.price(leg.instrument_id, PriceType.MID)
            if market_price is None:
                market_price = self._cache.price(leg.instrument_id, PriceType.LAST)

            if market_price is not None:
                leg_market_prices[leg.instrument_id] = float(market_price)
                total_weighted_price += leg.ratio * float(market_price)

        # Calculate adjustment factor
        if total_weighted_price != 0:
            adjustment_factor = combo_price_float / total_weighted_price
        else:
            adjustment_factor = 1.0

        # Calculate adjusted leg prices
        for leg in combo.legs:
            if leg.instrument_id in leg_market_prices:
                adjusted_price = leg_market_prices[leg.instrument_id] * adjustment_factor

                # Get instrument for price precision
                instrument = self._cache.instrument(leg.instrument_id)
                if instrument:
                    precision = instrument.price_precision
                    leg_prices[leg.instrument_id] = Price.from_str(
                        f"{adjusted_price:.{precision}f}",
                    )
                else:
                    leg_prices[leg.instrument_id] = Price.from_str(f"{adjusted_price:.2f}")
            else:
                # No market price available, use proportional allocation
                # For demo purposes, allocate combo price proportionally by weight
                if len(combo.legs) > 0:
                    proportional_price = combo_price_float * abs(leg.weight) / len(combo.legs)
                    leg_prices[leg.instrument_id] = Price.from_str(f"{proportional_price:.2f}")
                else:
                    raise ValueError(f"No price provided for leg {leg.instrument_id}")

        return leg_prices

    def get_combo_order_info(self, combo_order_id: ClientOrderId) -> dict[str, Any] | None:
        """
        Get information about a combo order.

        Parameters
        ----------
        combo_order_id : ClientOrderId
            The combo order ID.

        Returns
        -------
        dict[str, Any] or None
            The combo order information, or None if not found.

        """
        return self._combo_orders.get(combo_order_id)

    def get_combo_order_by_leg(self, leg_order_id: ClientOrderId) -> ClientOrderId | None:
        """
        Get the combo order ID for a given leg order ID.

        Parameters
        ----------
        leg_order_id : ClientOrderId
            The leg order ID.

        Returns
        -------
        ClientOrderId or None
            The combo order ID, or None if not found.

        """
        return self._leg_orders.get(leg_order_id)

    def remove_combo_order(self, combo_order_id: ClientOrderId) -> None:
        """
        Remove a combo order from tracking.

        Parameters
        ----------
        combo_order_id : ClientOrderId
            The combo order ID to remove.

        """
        if combo_order_id in self._combo_orders:
            combo_info = self._combo_orders[combo_order_id]

            # Remove leg order mappings
            for leg_order_id in combo_info["leg_orders"]:
                self._leg_orders.pop(leg_order_id, None)

            # Remove combo order
            del self._combo_orders[combo_order_id]

            self._logger.info(f"Removed combo order tracking: {combo_order_id}")
