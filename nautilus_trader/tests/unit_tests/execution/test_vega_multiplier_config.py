#!/usr/bin/env python3
# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2024 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------


from nautilus_trader.common.component import Logger
from nautilus_trader.execution.config import ExecEngineConfig
from nautilus_trader.execution.engine import ExecutionEngine
from nautilus_trader.model.combo_pricing import ComboPricingEngine
from nautilus_trader.test_kit.stubs.component import TestComponentStubs


class TestVegaMultiplierConfig:
    """
    Test vega multiplier configuration flow.
    """

    def setup_method(self):
        """
        Set up test fixtures.
        """
        self.msgbus = TestComponentStubs.msgbus()
        self.cache = TestComponentStubs.cache()
        self.clock = TestComponentStubs.clock()
        self.logger = Logger(name="TEST_LOGGER")

    def test_exec_engine_config_default_vega_multiplier(self):
        """
        Test that ExecEngineConfig has default vega_multiplier of 1.0.
        """
        # Arrange & Act
        config = ExecEngineConfig()

        # Assert
        assert config.vega_multiplier == 1.0

    def test_exec_engine_config_custom_vega_multiplier(self):
        """
        Test that ExecEngineConfig accepts custom vega_multiplier.
        """
        # Arrange & Act
        config = ExecEngineConfig(vega_multiplier=2.5)

        # Assert
        assert config.vega_multiplier == 2.5

    def test_exec_engine_stores_vega_multiplier(self):
        """
        Test that ExecutionEngine stores vega_multiplier from config.
        """
        # Arrange
        config = ExecEngineConfig(vega_multiplier=1.8)

        # Act
        engine = ExecutionEngine(
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
            config=config,
        )

        # Assert
        assert engine.vega_multiplier == 1.8
        assert engine.combo_vega_multiplier == 1.8

    def test_exec_engine_default_vega_multiplier(self):
        """
        Test that ExecutionEngine uses default vega_multiplier when no config provided.
        """
        # Arrange & Act
        engine = ExecutionEngine(
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
        )

        # Assert
        assert engine.vega_multiplier == 1.0
        assert engine.combo_vega_multiplier == 1.0

    def test_combo_pricing_engine_default_vega_multiplier(self):
        """
        Test that ComboPricingEngine uses default vega_multiplier.
        """
        # Arrange & Act
        pricing_engine = ComboPricingEngine(
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
            logger=self.logger,
        )

        # Assert
        assert pricing_engine._vega_multiplier == 1.0

    def test_combo_pricing_engine_custom_vega_multiplier(self):
        """
        Test that ComboPricingEngine accepts custom vega_multiplier.
        """
        # Arrange & Act
        pricing_engine = ComboPricingEngine(
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
            logger=self.logger,
            vega_multiplier=3.2,
        )

        # Assert
        assert pricing_engine._vega_multiplier == 3.2

    def test_vega_multiplier_config_validation(self):
        """
        Test that vega_multiplier accepts positive values as percentages.
        """
        # Test that positive values are accepted (representing percentages)
        config = ExecEngineConfig(vega_multiplier=1.5)  # 1.5% uncertainty
        assert config.vega_multiplier == 1.5

        # Test typical percentage values
        config_1pct = ExecEngineConfig(vega_multiplier=1.0)  # 1% uncertainty
        assert config_1pct.vega_multiplier == 1.0

        config_2pct = ExecEngineConfig(vega_multiplier=2.0)  # 2% uncertainty
        assert config_2pct.vega_multiplier == 2.0

    def test_vega_calculation_concept(self):
        """
        Test the concept of vega-based spread calculation.
        """
        # This test demonstrates the vega-based spread calculation
        # In the actual implementation, vega values are used directly from Greeks:
        # total_vega = sum(abs(leg.ratio * leg.vega) for leg in combo.legs)
        # spread_multiplier = 1.0 + (total_vega * vega_multiplier)

        # Example: If combo has total vega = 0.3, vega_multiplier = 2.0
        total_vega = 0.3  # Combined vega from all legs
        vega_multiplier = 2.0  # Multiplier for spread calculation

        # Apply multiplier directly to vega
        spread_multiplier = 1.0 + (total_vega * vega_multiplier)
        # 1.0 + (0.3 * 2.0) = 1.0 + 0.6 = 1.6

        expected_multiplier = 1.6
        assert abs(spread_multiplier - expected_multiplier) < 1e-6

    def test_vega_multiplier_integration_flow(self):
        """
        Test complete flow from config to pricing engine.
        """
        # Arrange
        config = ExecEngineConfig(vega_multiplier=2.0)
        engine = ExecutionEngine(
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
            config=config,
        )

        # Act - Create pricing engine with engine's vega_multiplier
        pricing_engine = ComboPricingEngine(
            msgbus=self.msgbus,
            cache=self.cache,
            clock=self.clock,
            logger=self.logger,
            vega_multiplier=engine.combo_vega_multiplier,
        )

        # Assert
        assert config.vega_multiplier == 2.0
        assert engine.vega_multiplier == 2.0
        assert engine.combo_vega_multiplier == 2.0
        assert pricing_engine._vega_multiplier == 2.0
