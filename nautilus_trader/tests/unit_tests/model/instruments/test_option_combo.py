# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

import pytest
from decimal import Decimal

from nautilus_trader.core.rust.model import AssetClass
from nautilus_trader.core.rust.model import OrderSide
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import Symbol
from nautilus_trader.model.instruments.option_combo import ComboLeg
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.currencies import USD
from nautilus_trader.model.objects import Currency
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity


class TestComboLeg:
    """Test cases for ComboLeg."""
    
    def test_combo_leg_creation(self):
        """Test ComboLeg creation with valid parameters."""
        leg = ComboLeg(
            instrument_id=InstrumentId.from_str("SPY240315C00500000.OPRA"),
            ratio=2,  # Positive = BUY
        )

        assert leg.instrument_id == InstrumentId.from_str("SPY240315C00500000.OPRA")
        assert leg.side == OrderSide.BUY  # Property derived from positive ratio
        assert leg.ratio == 2
        assert leg.abs_ratio == 2
    
    def test_combo_leg_sell_side_ratio(self):
        """Test ComboLeg ratio calculation for SELL side."""
        leg = ComboLeg(
            instrument_id=InstrumentId.from_str("SPY240315C00500000.OPRA"),
            ratio=-3,  # Negative = SELL
        )

        assert leg.side == OrderSide.SELL  # Property derived from negative ratio
        assert leg.ratio == -3
        assert leg.abs_ratio == 3
    
    def test_combo_leg_custom_ratio(self):
        """Test ComboLeg with custom ratio."""
        leg = ComboLeg(
            instrument_id=InstrumentId.from_str("SPY240315C00500000.OPRA"),
            ratio=1,  # Positive = BUY
        )

        assert leg.side == OrderSide.BUY
        assert leg.ratio == 1

    def test_combo_leg_invalid_ratio(self):
        """Test ComboLeg creation with invalid ratio."""
        with pytest.raises(ValueError):
            ComboLeg(
                instrument_id=InstrumentId.from_str("SPY240315C00500000.OPRA"),
                ratio=0,  # Invalid: cannot be zero
            )
    
    def test_combo_leg_str_representation(self):
        """Test ComboLeg string representation."""
        leg = ComboLeg(
            instrument_id=InstrumentId.from_str("SPY240315C00500000.OPRA"),
            ratio=2,  # Positive = BUY
        )

        expected = "BUY 2 SPY240315C00500000.OPRA"
        assert str(leg) == expected


class TestOptionCombo:
    """Test cases for OptionCombo."""
    
    def setup_method(self):
        """Set up test fixtures."""
        self.legs = [
            ComboLeg(
                instrument_id=InstrumentId.from_str("SPY240315C00500000.OPRA"),
                ratio=1,  # Positive = BUY
            ),
            ComboLeg(
                instrument_id=InstrumentId.from_str("SPY240315P00500000.OPRA"),
                ratio=1,  # Positive = BUY
            ),
        ]
        
        self.combo = OptionCombo(
            raw_symbol=Symbol("SPY_STRD_240315"),
            asset_class=AssetClass.EQUITY,
            currency=USD,
            price_precision=2,
            price_increment=Price.from_str("0.01"),
            multiplier=Quantity.from_int(100),
            lot_size=Quantity.from_int(1),
            underlying="SPY",
            strategy_type="STRADDLE",
            legs=self.legs,
            vega_multiplier=0.2,
            activation_ns=1700000000000000000,
            expiration_ns=1710547200000000000,
            ts_event=1700000000000000000,
            ts_init=1700000000000000000,
            instrument_id=InstrumentId.from_str("SPY_STRADDLE_240315.COMBO"),  # Explicit ID for test
        )
    
    def test_option_combo_creation(self):
        """Test OptionCombo creation with valid parameters."""
        assert self.combo.id == InstrumentId.from_str("SPY_STRADDLE_240315.COMBO")
        assert self.combo.underlying == "SPY"
        assert self.combo.strategy_type == "STRADDLE"
        assert len(self.combo.legs) == 2
        assert self.combo.vega_multiplier == 0.2
    
    def test_option_combo_invalid_legs_empty(self):
        """Test OptionCombo creation with empty legs list."""
        with pytest.raises(ValueError):
            OptionCombo(
                instrument_id=InstrumentId.from_str("SPY_INVALID.COMBO"),
                raw_symbol=Symbol("SPY_INVALID"),
                asset_class=AssetClass.EQUITY,
                currency=USD,
                price_precision=2,
                price_increment=Price.from_str("0.01"),
                multiplier=Quantity.from_int(100),
                lot_size=Quantity.from_int(1),
                underlying="SPY",
                strategy_type="INVALID",
                legs=[],  # Empty legs
                vega_multiplier=0.1,
                activation_ns=1700000000000000000,
                expiration_ns=1710547200000000000,
                ts_event=1700000000000000000,
                ts_init=1700000000000000000,
            )
    
    def test_option_combo_invalid_legs_too_many(self):
        """Test OptionCombo creation with too many legs."""
        too_many_legs = [
            ComboLeg(InstrumentId.from_str(f"SPY240315C0050000{i}.OPRA"), 1)  # Positive = BUY
            for i in range(5)  # 5 legs, max is 4
        ]
        
        with pytest.raises(ValueError):
            OptionCombo(
                instrument_id=InstrumentId.from_str("SPY_INVALID.COMBO"),
                raw_symbol=Symbol("SPY_INVALID"),
                asset_class=AssetClass.EQUITY,
                currency=USD,
                price_precision=2,
                price_increment=Price.from_str("0.01"),
                multiplier=Quantity.from_int(100),
                lot_size=Quantity.from_int(1),
                underlying="SPY",
                strategy_type="INVALID",
                legs=too_many_legs,
                vega_multiplier=0.1,
                activation_ns=1700000000000000000,
                expiration_ns=1710547200000000000,
                ts_event=1700000000000000000,
                ts_init=1700000000000000000,
            )
    
    def test_get_leg_instrument_ids(self):
        """Test getting leg instrument IDs."""
        leg_ids = self.combo.get_leg_instrument_ids()
        
        expected_ids = [
            InstrumentId.from_str("SPY240315C00500000.OPRA"),
            InstrumentId.from_str("SPY240315P00500000.OPRA"),
        ]
        
        assert leg_ids == expected_ids
    
    def test_calculate_combo_price(self):
        """Test combo price calculation."""
        leg_prices = {
            InstrumentId.from_str("SPY240315C00500000.OPRA"): Price.from_str("5.50"),
            InstrumentId.from_str("SPY240315P00500000.OPRA"): Price.from_str("4.25"),
        }
        
        combo_price = self.combo.calculate_combo_price(leg_prices)
        
        # Expected: 1.0 * 5.50 + 1.0 * 4.25 = 9.75
        assert combo_price == Price.from_str("9.75")
    
    def test_calculate_combo_price_missing_leg(self):
        """Test combo price calculation with missing leg price."""
        leg_prices = {
            InstrumentId.from_str("SPY240315C00500000.OPRA"): Price.from_str("5.50"),
            # Missing put price
        }
        
        combo_price = self.combo.calculate_combo_price(leg_prices)
        assert combo_price is None
    
    def test_calculate_vega_weighted_spread(self):
        """Test vega-weighted spread calculation."""
        leg_vegas = {
            InstrumentId.from_str("SPY240315C00500000.OPRA"): 0.15,
            InstrumentId.from_str("SPY240315P00500000.OPRA"): 0.12,
        }
        
        spread = self.combo.calculate_vega_weighted_spread(leg_vegas, base_spread=0.01)
        
        # Expected: 0.01 * (1.0 + (|1.0 * 0.15| + |1.0 * 0.12|) * 0.2)
        # = 0.01 * (1.0 + 0.27 * 0.2) = 0.01 * 1.054 = 0.01054
        expected = 0.01 * (1.0 + 0.27 * 0.2)
        assert abs(spread - expected) < 1e-6
    
    def test_combo_serialization(self):
        """Test combo serialization to/from dict."""
        combo_dict = self.combo.to_dict()
        
        # Verify key fields
        assert combo_dict["type"] == "OptionCombo"
        assert combo_dict["id"] == str(self.combo.id)
        assert combo_dict["strategy_type"] == "STRADDLE"
        assert combo_dict["underlying"] == "SPY"
        assert len(combo_dict["legs"]) == 2
        
        # Verify leg data
        leg_data = combo_dict["legs"][0]
        assert leg_data["instrument_id"] == "SPY240315C00500000.OPRA"
        assert leg_data["ratio"] == 1  # Positive ratio = BUY
        assert leg_data["ratio"] == 1
        
        # Test deserialization
        recreated_combo = OptionCombo.from_dict(combo_dict)
        assert recreated_combo.id == self.combo.id
        assert recreated_combo.strategy_type == self.combo.strategy_type
        assert len(recreated_combo.legs) == len(self.combo.legs)
    
    def test_combo_str_representation(self):
        """Test combo string representation."""
        combo_str = str(self.combo)
        
        assert "STRADDLE" in combo_str
        assert "SPY" in combo_str
        assert "BUY 1 SPY240315C00500000.OPRA" in combo_str
        assert "BUY 1 SPY240315P00500000.OPRA" in combo_str
    
    def test_combo_properties(self):
        """Test combo datetime properties."""
        # Test activation property
        activation = self.combo.activation
        assert activation is not None
        
        # Test expiration property
        expiration = self.combo.expiration
        assert expiration is not None
        
        # Test is_expired property (should be True for past date)
        is_expired = self.combo.is_expired
        assert isinstance(is_expired, bool)
