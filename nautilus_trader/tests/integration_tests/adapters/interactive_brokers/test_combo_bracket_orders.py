# -------------------------------------------------------------------------------------------------
#  Copyright (C) 2015-2025 Nautech Systems Pty Ltd. All rights reserved.
#  https://nautechsystems.io
#
#  Licensed under the GNU Lesser General Public License Version 3.0 (the "License");
#  You may not use this file except in compliance with the License.
#  You may obtain a copy of the License at https://www.gnu.org/licenses/lgpl-3.0.en.html
#
#  Unless required by applicable law or agreed to in writing, software
#  distributed under the License is distributed on an "AS IS" BASIS,
#  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
#  See the License for the specific language governing permissions and
#  limitations under the License.
# -------------------------------------------------------------------------------------------------

import pytest
from unittest.mock import Mock, patch

from nautilus_trader.common.factories import OrderFactory
from nautilus_trader.common.component import TestClock
from nautilus_trader.model.identifiers import InstrumentId, TraderId, StrategyId, Symbol
from nautilus_trader.model.enums import OrderSide, AssetClass
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.model.instruments.option_combo import ComboLeg, OptionCombo
from nautilus_trader.model.currencies import USD
from nautilus_trader.adapters.interactive_brokers.execution import InteractiveBrokersExecutionClient



class TestComboBracketOrders:
    """Test cases for bracket orders with combo instruments in IB adapter."""

    def setup_method(self):
        """Set up test fixtures."""
        # Create combo instrument
        self.legs = [
            ComboLeg(
                instrument_id=InstrumentId.from_str('SPY240315C00520000.OPRA'),
                ratio=1,  # BUY call
            ),
            ComboLeg(
                instrument_id=InstrumentId.from_str('SPY240315P00480000.OPRA'),
                ratio=-1,  # SELL put
            ),
        ]

        self.combo = OptionCombo(
            raw_symbol=Symbol('SPY_STRANGLE'),
            asset_class=AssetClass.EQUITY,
            currency=USD,
            price_precision=2,
            price_increment=Price.from_str('0.01'),
            multiplier=Quantity.from_int(100),
            lot_size=Quantity.from_int(1),
            underlying='SPY',
            strategy_type='STRANGLE',
            legs=self.legs,
            vega_multiplier=0.2,
            activation_ns=0,
            expiration_ns=1710460800000000000,
            ts_event=0,
            ts_init=0,
            instrument_id=InstrumentId.from_str('SPY_STRANGLE.COMBO'),  # Explicit ID
        )

        # Create order factory
        self.clock = TestClock()
        self.order_factory = OrderFactory(
            trader_id=TraderId('TESTER-001'),
            strategy_id=StrategyId('TEST-001'),
            clock=self.clock,
        )

    def test_bracket_order_creation_with_combo(self):
        """Test that bracket orders can be created with combo instruments."""
        # Create bracket order
        bracket_orders = self.order_factory.bracket(
            instrument_id=self.combo.id,
            order_side=OrderSide.BUY,
            quantity=Quantity.from_int(1),
            sl_trigger_price=Price.from_str('1.00'),
            tp_price=Price.from_str('5.00'),
        )

        # Verify bracket order structure
        assert len(bracket_orders.orders) == 3
        assert all(order.instrument_id == self.combo.id for order in bracket_orders.orders)

        # Verify order types
        entry_order = bracket_orders.orders[0]
        stop_order = bracket_orders.orders[1]
        limit_order = bracket_orders.orders[2]

        assert entry_order.order_type.name == 'MARKET'
        assert stop_order.order_type.name == 'STOP_MARKET'
        assert limit_order.order_type.name == 'LIMIT'

        # Verify sides
        assert entry_order.side == OrderSide.BUY
        assert stop_order.side == OrderSide.SELL  # Opposite of entry
        assert limit_order.side == OrderSide.SELL  # Opposite of entry

    @patch('nautilus_trader.adapters.interactive_brokers.execution.InteractiveBrokersExecutionClient._submit_order_list')
    def test_combo_mapping_storage_for_bracket_orders(self, mock_submit):
        """Test that combo mappings are stored for bracket orders."""
        # Create mock execution client
        exec_client = Mock(spec=InteractiveBrokersExecutionClient)
        exec_client._combo_order_mappings = {}
        exec_client.instrument_provider = Mock()
        exec_client.instrument_provider.find.return_value = self.combo

        # Create bracket order
        bracket_orders = self.order_factory.bracket(
            instrument_id=self.combo.id,
            order_side=OrderSide.BUY,
            quantity=Quantity.from_int(1),
            sl_trigger_price=Price.from_str('1.00'),
            tp_price=Price.from_str('5.00'),
        )

        # Simulate the combo mapping logic from _submit_order_list
        for nautilus_order in bracket_orders.orders:
            instrument = self.combo  # Mock instrument lookup
            if hasattr(instrument, 'legs') and instrument.legs:
                order_id = str(hash(nautilus_order.client_order_id))  # Mock order ID
                exec_client._combo_order_mappings[order_id] = {
                    'combo_instrument': instrument,
                    'nautilus_order': nautilus_order,
                    'leg_instrument_ids': [leg.instrument_id for leg in instrument.legs],
                    'leg_sides': [leg.side for leg in instrument.legs],
                    'leg_ratios': [leg.ratio for leg in instrument.legs],
                }

        # Verify combo mappings were created for all orders
        assert len(exec_client._combo_order_mappings) == 3

        # Verify mapping structure for each order
        for order_id, mapping in exec_client._combo_order_mappings.items():
            assert mapping['combo_instrument'] == self.combo
            assert len(mapping['leg_instrument_ids']) == 2
            assert mapping['leg_instrument_ids'][0] == InstrumentId.from_str('SPY240315C00520000.OPRA')
            assert mapping['leg_instrument_ids'][1] == InstrumentId.from_str('SPY240315P00480000.OPRA')
            assert mapping['leg_sides'][0] == OrderSide.BUY  # Positive ratio
            assert mapping['leg_sides'][1] == OrderSide.SELL  # Negative ratio
            assert mapping['leg_ratios'][0] == 1
            assert mapping['leg_ratios'][1] == -1

    def test_combo_leg_signed_ratios(self):
        """Test that combo legs use signed ratios correctly."""
        # Test positive ratio (BUY)
        buy_leg = self.legs[0]
        assert buy_leg.ratio == 1
        assert buy_leg.side == OrderSide.BUY
        assert buy_leg.abs_ratio == 1

        # Test negative ratio (SELL)
        sell_leg = self.legs[1]
        assert sell_leg.ratio == -1
        assert sell_leg.side == OrderSide.SELL
        assert sell_leg.abs_ratio == 1

    def test_combo_serialization_with_signed_ratios(self):
        """Test that combo serialization works with signed ratios."""
        combo_dict = self.combo.to_dict()

        # Verify leg serialization
        assert len(combo_dict["legs"]) == 2

        # First leg (BUY)
        leg1_data = combo_dict["legs"][0]
        assert leg1_data["instrument_id"] == "SPY240315C00520000.OPRA"
        assert leg1_data["ratio"] == 1  # Positive = BUY

        # Second leg (SELL)
        leg2_data = combo_dict["legs"][1]
        assert leg2_data["instrument_id"] == "SPY240315P00480000.OPRA"
        assert leg2_data["ratio"] == -1  # Negative = SELL

        # Test deserialization
        recreated_combo = OptionCombo.from_dict(combo_dict)
        assert len(recreated_combo.legs) == 2
        assert recreated_combo.legs[0].ratio == 1
        assert recreated_combo.legs[1].ratio == -1
        assert recreated_combo.legs[0].side == OrderSide.BUY
        assert recreated_combo.legs[1].side == OrderSide.SELL

    def test_order_factory_methods_with_combo(self):
        """Test that various order factory methods work with combo instruments."""
        # Test market order
        market_order = self.order_factory.market(
            instrument_id=self.combo.id,
            order_side=OrderSide.BUY,
            quantity=Quantity.from_int(1),
        )
        assert market_order.instrument_id == self.combo.id
        assert market_order.order_type.name == 'MARKET'

        # Test limit order
        limit_order = self.order_factory.limit(
            instrument_id=self.combo.id,
            order_side=OrderSide.SELL,
            quantity=Quantity.from_int(2),
            price=Price.from_str('3.50'),
        )
        assert limit_order.instrument_id == self.combo.id
        assert limit_order.order_type.name == 'LIMIT'
        assert limit_order.price == Price.from_str('3.50')

        # Test stop market order
        stop_order = self.order_factory.stop_market(
            instrument_id=self.combo.id,
            order_side=OrderSide.BUY,
            quantity=Quantity.from_int(1),
            trigger_price=Price.from_str('2.00'),
        )
        assert stop_order.instrument_id == self.combo.id
        assert stop_order.order_type.name == 'STOP_MARKET'
        assert stop_order.trigger_price == Price.from_str('2.00')
