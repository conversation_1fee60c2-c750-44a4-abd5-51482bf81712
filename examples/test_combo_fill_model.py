#!/usr/bin/env python3
"""
Test script demonstrating the advanced option combo fill model.

This script shows how the ComboFillModel provides realistic execution probability
based on order positioning relative to bid-ask spreads and market conditions.
"""

import asyncio
from decimal import Decimal

from nautilus_trader.backtest.combo_fill_model import ComboFillModel
from nautilus_trader.backtest.combo_execution import EnhancedComboExecutionHandler
from nautilus_trader.cache.cache import Cache
from nautilus_trader.common.component import Logger
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.model.data import QuoteTick
from nautilus_trader.model.enums import AssetClass, OptionKind, OrderSide, OrderType, TimeInForce
from nautilus_trader.model.identifiers import InstrumentId, Symbol, Venue, TraderId, StrategyId
from nautilus_trader.model.instruments.option_combo import OptionCombo, ComboLeg
from nautilus_trader.model.instruments.option_contract import OptionContract
from nautilus_trader.model.currencies import USD
from nautilus_trader.model.objects import Price, Quantity
from nautilus_trader.model.orders import LimitOrder
from nautilus_trader.test_kit.providers import TestInstrumentProvider
from nautilus_trader.test_kit.stubs.identifiers import TestIdStubs


def create_test_option_contracts():
    """Create test option contracts for combo testing."""
    # Create simple option contracts for testing
    call_150 = OptionContract(
        instrument_id=InstrumentId(Symbol("AAPL240315C00150000"), Venue("NASDAQ")),
        raw_symbol=Symbol("AAPL240315C00150000"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="AAPL",
        option_kind=OptionKind.CALL,
        strike_price=Price.from_str("150.00"),
        activation_ns=0,
        expiration_ns=1710460800000000000,  # March 15, 2024
        ts_event=0,
        ts_init=0,
    )

    put_145 = OptionContract(
        instrument_id=InstrumentId(Symbol("AAPL240315P00145000"), Venue("NASDAQ")),
        raw_symbol=Symbol("AAPL240315P00145000"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="AAPL",
        option_kind=OptionKind.PUT,
        strike_price=Price.from_str("145.00"),
        activation_ns=0,
        expiration_ns=1710460800000000000,  # March 15, 2024
        ts_event=0,
        ts_init=0,
    )

    return call_150, put_145


def create_test_combo(call_contract, put_contract):
    """Create a test option combo (strangle)."""
    legs = [
        ComboLeg(instrument_id=call_contract.id, ratio=1),  # Long call
        ComboLeg(instrument_id=put_contract.id, ratio=1),   # Long put
    ]

    combo = OptionCombo(
        raw_symbol=Symbol("AAPL_STRANGLE_150_145"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="AAPL",
        strategy_type="STRANGLE",
        legs=legs,
        vega_multiplier=0.2,
        activation_ns=0,
        expiration_ns=1710460800000000000,  # March 15, 2024
        ts_event=0,
        ts_init=0,
        instrument_id=InstrumentId(Symbol("AAPL_STRANGLE_150_145"), Venue("NASDAQ")),
    )

    return combo


def setup_market_data(cache, call_contract, put_contract):
    """Setup market data for the option contracts."""
    # Add instruments to cache
    cache.add_instrument(call_contract)
    cache.add_instrument(put_contract)
    
    # Create quote ticks for the options
    call_quote = QuoteTick(
        instrument_id=call_contract.id,
        bid_price=Price.from_str("5.20"),
        ask_price=Price.from_str("5.40"),
        bid_size=Quantity.from_int(10),
        ask_size=Quantity.from_int(10),
        ts_event=0,
        ts_init=0,
    )
    
    put_quote = QuoteTick(
        instrument_id=put_contract.id,
        bid_price=Price.from_str("3.80"),
        ask_price=Price.from_str("4.00"),
        bid_size=Quantity.from_int(15),
        ask_size=Quantity.from_int(15),
        ts_event=0,
        ts_init=0,
    )
    
    # Add quotes to cache
    cache.add_quote_tick(call_quote)
    cache.add_quote_tick(put_quote)
    
    return call_quote, put_quote


def create_test_order(combo, order_price, side=OrderSide.BUY):
    """Create a test limit order for the combo."""
    return LimitOrder(
        trader_id=TraderId("TRADER-001"),
        strategy_id=StrategyId("TEST-001"),
        instrument_id=combo.id,
        client_order_id=TestIdStubs.client_order_id(),
        order_side=side,
        quantity=Quantity.from_int(5),
        price=order_price,
        init_id=UUID4(),
        ts_init=0,
        time_in_force=TimeInForce.GTC,
    )


def test_fill_model_scenarios():
    """Test various fill model scenarios."""
    print("=== Option Combo Fill Model Test ===\n")
    
    # Setup
    cache = Cache()
    logger = Logger("TEST_LOGGER")
    
    # Create test instruments
    call_contract, put_contract = create_test_option_contracts()
    combo = create_test_combo(call_contract, put_contract)
    
    # Setup market data
    call_quote, put_quote = setup_market_data(cache, call_contract, put_contract)
    
    # Natural combo price: (5.20 + 5.40)/2 + (3.80 + 4.00)/2 = 5.30 + 3.90 = 9.20
    natural_price = Price.from_str("9.20")
    
    print(f"Call option quote: {call_quote.bid_price} x {call_quote.ask_price}")
    print(f"Put option quote: {put_quote.bid_price} x {put_quote.ask_price}")
    print(f"Natural combo price: {natural_price}\n")
    
    # Create fill model with different configurations
    scenarios = [
        ("Conservative", ComboFillModel(base_fill_prob=0.6, spread_sensitivity=1.5)),
        ("Aggressive", ComboFillModel(base_fill_prob=0.9, spread_sensitivity=3.0)),
        ("Realistic", ComboFillModel(base_fill_prob=0.8, spread_sensitivity=2.0, partial_fill_prob=0.4)),
    ]
    
    # Test different order prices
    test_prices = [
        ("Very Aggressive Buy", Price.from_str("9.50")),  # Above natural
        ("Aggressive Buy", Price.from_str("9.30")),       # Slightly above natural
        ("At Natural", Price.from_str("9.20")),           # At natural price
        ("Passive Buy", Price.from_str("9.00")),          # Below natural
        ("Very Passive Buy", Price.from_str("8.80")),     # Well below natural
    ]
    
    for scenario_name, fill_model in scenarios:
        print(f"--- {scenario_name} Fill Model ---")
        
        for price_desc, test_price in test_prices:
            order = create_test_order(combo, test_price)
            
            # Calculate fill probability
            fill_prob, partial_ratio = fill_model.calculate_combo_fill_probability(
                order, combo, cache
            )
            
            # Test multiple executions to show probability distribution
            fills = 0
            partial_fills = 0
            total_tests = 100
            
            for _ in range(total_tests):
                should_fill, fill_qty = fill_model.is_combo_limit_filled(order, combo, cache)
                if should_fill:
                    fills += 1
                    if fill_qty and fill_qty < order.quantity:
                        partial_fills += 1
            
            actual_fill_rate = fills / total_tests
            partial_fill_rate = partial_fills / max(fills, 1)
            
            print(f"  {price_desc:20} @ {test_price}: "
                  f"Prob={fill_prob:.2f}, Actual={actual_fill_rate:.2f}, "
                  f"Partial={partial_fill_rate:.2f}")
        
        print()


def test_enhanced_execution_handler():
    """Test the enhanced execution handler with fill model integration."""
    print("=== Enhanced Execution Handler Test ===\n")
    
    # Setup
    cache = Cache()
    logger = Logger("TEST_HANDLER")
    
    # Create test instruments
    call_contract, put_contract = create_test_option_contracts()
    combo = create_test_combo(call_contract, put_contract)
    
    # Setup market data
    setup_market_data(cache, call_contract, put_contract)
    
    # Create enhanced handler
    fill_model = ComboFillModel(
        base_fill_prob=0.8,
        spread_sensitivity=2.0,
        partial_fill_prob=0.3,
        random_seed=42,  # For reproducible results
    )
    
    handler = EnhancedComboExecutionHandler(cache, logger, fill_model)
    
    # Test orders at different price levels
    test_orders = [
        create_test_order(combo, Price.from_str("9.40")),  # Aggressive
        create_test_order(combo, Price.from_str("9.20")),  # Natural
        create_test_order(combo, Price.from_str("9.00")),  # Passive
    ]
    
    for i, order in enumerate(test_orders):
        print(f"Order {i+1}: {order.side.name} {order.quantity} @ {order.price}")
        
        # Register order
        handler.register_combo_order_with_model(order.client_order_id, combo, order)
        
        # Get fill probability info
        prob_info = handler.get_fill_probability_info(order)
        if prob_info:
            print(f"  Fill Probability: {prob_info['fill_probability']:.3f}")
            if prob_info['partial_fill_ratio']:
                print(f"  Partial Fill Ratio: {prob_info['partial_fill_ratio']:.3f}")
        
        # Test if order should fill
        should_fill, fill_qty = handler.should_fill_combo_order(order)
        print(f"  Should Fill: {should_fill}")
        if fill_qty:
            print(f"  Fill Quantity: {fill_qty}")
        
        print()


if __name__ == "__main__":
    test_fill_model_scenarios()
    test_enhanced_execution_handler()
    print("Fill model testing completed!")
