#!/usr/bin/env python3
"""
Enhanced Option Combo Fill Model Test Suite

This test demonstrates all the advanced features of the enhanced combo fill model:
- Volatility-based adjustments
- Time-of-day liquidity variations  
- Market maker vs retail execution profiles
- Market microstructure considerations
"""

import random
from datetime import datetime, time
from decimal import Decimal
from uuid import UUID

from nautilus_trader.core.uuid import UUID4

from nautilus_trader.backtest.combo_execution import EnhancedComboExecutionHandler
from nautilus_trader.backtest.combo_fill_model import ComboFillModel
from nautilus_trader.cache.cache import Cache
from nautilus_trader.common.component import TestClock
from nautilus_trader.common.component import Logger
from nautilus_trader.model.data import QuoteTick
from nautilus_trader.model.enums import AssetClass, OrderSide, OptionKind, TimeInForce
from nautilus_trader.model.identifiers import InstrumentId, Symbol, Venue, TraderId, StrategyId
from nautilus_trader.model.instruments.option_combo import ComboLeg, OptionCombo
from nautilus_trader.model.instruments.option_contract import OptionContract
from nautilus_trader.model.objects import Price, Quantity, Money, Currency
from nautilus_trader.model.orders import LimitOrder
from nautilus_trader.test_kit.stubs.identifiers import TestIdStubs


def create_enhanced_test_combo():
    """Create a test option combo with realistic parameters."""
    # Create call option
    call_id = InstrumentId(Symbol("AAPL250117C00150000"), Venue("OPRA"))
    call_option = OptionContract(
        instrument_id=call_id,
        raw_symbol=Symbol("AAPL250117C00150000"),
        asset_class=AssetClass.EQUITY,
        currency=Currency.from_str("USD"),
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="AAPL",
        option_kind=OptionKind.CALL,
        activation_ns=0,
        expiration_ns=1_800_000_000_000_000_000,  # ~2027
        strike_price=Price.from_str("150.00"),
        ts_event=0,
        ts_init=0,
    )
    
    # Create put option
    put_id = InstrumentId(Symbol("AAPL250117P00145000"), Venue("OPRA"))
    put_option = OptionContract(
        instrument_id=put_id,
        raw_symbol=Symbol("AAPL250117P00145000"),
        asset_class=AssetClass.EQUITY,
        currency=Currency.from_str("USD"),
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="AAPL",
        option_kind=OptionKind.PUT,
        activation_ns=0,
        expiration_ns=1_800_000_000_000_000_000,  # ~2027
        strike_price=Price.from_str("145.00"),
        ts_event=0,
        ts_init=0,
    )
    
    # Create combo legs
    call_leg = ComboLeg(instrument_id=call_id, ratio=1)
    put_leg = ComboLeg(instrument_id=put_id, ratio=-1)
    
    # Create combo
    combo = OptionCombo(
        raw_symbol=Symbol("AAPL_COMBO_STRANGLE"),
        asset_class=AssetClass.EQUITY,
        currency=Currency.from_str("USD"),
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="AAPL",
        strategy_type="STRANGLE",
        legs=[call_leg, put_leg],
        instrument_id=InstrumentId(Symbol("AAPL_COMBO_STRANGLE"), Venue("OPRA")),
        ts_event=0,
        ts_init=0,
    )
    
    return combo, call_option, put_option


def create_market_data_scenarios():
    """Create different market data scenarios for testing."""
    scenarios = {}
    
    # High volatility scenario (wide spreads)
    scenarios["high_vol"] = {
        "call_quote": QuoteTick(
            instrument_id=InstrumentId(Symbol("AAPL250117C00150000"), Venue("OPRA")),
            bid_price=Price.from_str("4.50"),
            ask_price=Price.from_str("5.50"),  # Wide 1.00 spread
            bid_size=Quantity.from_int(10),
            ask_size=Quantity.from_int(10),
            ts_event=0,
            ts_init=0,
        ),
        "put_quote": QuoteTick(
            instrument_id=InstrumentId(Symbol("AAPL250117P00145000"), Venue("OPRA")),
            bid_price=Price.from_str("3.00"),
            ask_price=Price.from_str("4.20"),  # Wide 1.20 spread
            bid_size=Quantity.from_int(5),
            ask_size=Quantity.from_int(5),
            ts_event=0,
            ts_init=0,
        )
    }
    
    # Low volatility scenario (tight spreads)
    scenarios["low_vol"] = {
        "call_quote": QuoteTick(
            instrument_id=InstrumentId(Symbol("AAPL250117C00150000"), Venue("OPRA")),
            bid_price=Price.from_str("5.15"),
            ask_price=Price.from_str("5.25"),  # Tight 0.10 spread
            bid_size=Quantity.from_int(50),
            ask_size=Quantity.from_int(50),
            ts_event=0,
            ts_init=0,
        ),
        "put_quote": QuoteTick(
            instrument_id=InstrumentId(Symbol("AAPL250117P00145000"), Venue("OPRA")),
            bid_price=Price.from_str("3.85"),
            ask_price=Price.from_str("3.95"),  # Tight 0.10 spread
            bid_size=Quantity.from_int(30),
            ask_size=Quantity.from_int(30),
            ts_event=0,
            ts_init=0,
        )
    }
    
    # Normal volatility scenario
    scenarios["normal_vol"] = {
        "call_quote": QuoteTick(
            instrument_id=InstrumentId(Symbol("AAPL250117C00150000"), Venue("OPRA")),
            bid_price=Price.from_str("5.20"),
            ask_price=Price.from_str("5.40"),  # Normal 0.20 spread
            bid_size=Quantity.from_int(20),
            ask_size=Quantity.from_int(20),
            ts_event=0,
            ts_init=0,
        ),
        "put_quote": QuoteTick(
            instrument_id=InstrumentId(Symbol("AAPL250117P00145000"), Venue("OPRA")),
            bid_price=Price.from_str("3.80"),
            ask_price=Price.from_str("4.00"),  # Normal 0.20 spread
            bid_size=Quantity.from_int(15),
            ask_size=Quantity.from_int(15),
            ts_event=0,
            ts_init=0,
        )
    }
    
    return scenarios


def setup_cache_with_scenario(scenario_data):
    """Setup cache with specific market data scenario."""
    cache = Cache()
    
    # Add quotes to cache
    cache.add_quote_tick(scenario_data["call_quote"])
    cache.add_quote_tick(scenario_data["put_quote"])
    
    return cache


def create_test_order(combo, order_price, side=OrderSide.BUY):
    """Create a test limit order for the combo."""
    return LimitOrder(
        trader_id=TraderId("TRADER-001"),
        strategy_id=StrategyId("TEST-001"),
        instrument_id=combo.id,
        client_order_id=TestIdStubs.client_order_id(),
        order_side=side,
        quantity=Quantity.from_int(5),
        price=order_price,
        init_id=UUID4(),
        ts_init=0,
        time_in_force=TimeInForce.GTC,
    )


def test_enhanced_fill_model_scenarios():
    """Test enhanced fill model with various scenarios."""
    print("=== Enhanced Option Combo Fill Model Test ===\n")
    
    # Create test instruments
    combo, call_option, put_option = create_enhanced_test_combo()
    scenarios = create_market_data_scenarios()
    
    # Test different fill model configurations
    configs = [
        {
            "name": "Conservative Retail",
            "params": {
                "base_fill_prob": 0.6,
                "liquidity_factor": 0.6,
                "execution_profile": "retail",
                "volatility_adjustment": True,
                "time_of_day_adjustment": True,
            }
        },
        {
            "name": "Aggressive Market Maker",
            "params": {
                "base_fill_prob": 0.9,
                "liquidity_factor": 0.8,
                "execution_profile": "market_maker",
                "volatility_adjustment": True,
                "time_of_day_adjustment": True,
            }
        },
        {
            "name": "Realistic Retail (No Adjustments)",
            "params": {
                "base_fill_prob": 0.8,
                "liquidity_factor": 0.7,
                "execution_profile": "retail",
                "volatility_adjustment": False,
                "time_of_day_adjustment": False,
            }
        }
    ]
    
    for config in configs:
        print(f"--- {config['name']} ---")
        fill_model = ComboFillModel(**config["params"])
        
        for scenario_name, scenario_data in scenarios.items():
            cache = setup_cache_with_scenario(scenario_data)
            cache.add_instrument(call_option)
            cache.add_instrument(put_option)
            cache.add_instrument(combo)
            
            # Calculate natural combo price
            call_mid = (scenario_data["call_quote"].bid_price + scenario_data["call_quote"].ask_price) / 2
            put_mid = (scenario_data["put_quote"].bid_price + scenario_data["put_quote"].ask_price) / 2
            natural_price = call_mid - put_mid  # Long call, short put
            
            print(f"  {scenario_name.upper()} Scenario:")
            print(f"    Natural combo price: {natural_price:.2f}")
            
            # Test different order prices
            test_prices = [
                natural_price + Decimal("0.30"),  # Aggressive
                natural_price + Decimal("0.10"),  # Slightly aggressive
                natural_price,                    # At natural
                natural_price - Decimal("0.10"),  # Passive
                natural_price - Decimal("0.30"),  # Very passive
            ]
            
            for price in test_prices:
                order = create_test_order(combo, Price.from_str(str(price)))
                fill_prob, partial_ratio = fill_model.calculate_combo_fill_probability(
                    order, combo, cache
                )
                
                price_desc = "Aggressive" if price > natural_price else "Passive" if price < natural_price else "Natural"
                print(f"    {price_desc:>12} @ {price:.2f}: Prob={fill_prob:.3f}")
            
            print()
        
        print()


def test_time_of_day_variations():
    """Test time-of-day liquidity variations."""
    print("=== Time-of-Day Liquidity Variations Test ===\n")

    combo, call_option, put_option = create_enhanced_test_combo()
    scenarios = create_market_data_scenarios()
    cache = setup_cache_with_scenario(scenarios["normal_vol"])
    cache.add_instrument(call_option)
    cache.add_instrument(put_option)
    cache.add_instrument(combo)

    # Create fill model with time adjustments enabled
    fill_model = ComboFillModel(
        base_fill_prob=0.8,
        liquidity_factor=0.7,
        execution_profile="retail",
        volatility_adjustment=False,  # Disable to isolate time effects
        time_of_day_adjustment=True,
    )

    # Test different times of day
    test_times = [
        (time(9, 45), "Market Open"),
        (time(11, 0), "Mid-Morning"),
        (time(12, 30), "Lunch Hour"),
        (time(14, 0), "Afternoon"),
        (time(15, 30), "Market Close"),
        (time(18, 0), "After Hours"),
    ]

    natural_price = Price.from_str("9.20")
    order = create_test_order(combo, natural_price)

    print("Time-of-Day Fill Probability Variations:")
    for test_time, description in test_times:
        # Mock the time by temporarily replacing the method
        original_method = fill_model._calculate_time_of_day_adjustment
        fill_model._calculate_time_of_day_adjustment = lambda: original_method(
            datetime.combine(datetime.today(), test_time)
        )

        fill_prob, _ = fill_model.calculate_combo_fill_probability(order, combo, cache)

        # Restore original method
        fill_model._calculate_time_of_day_adjustment = original_method

        print(f"  {description:>12} ({test_time}): {fill_prob:.3f}")

    print()


def test_execution_profile_comparison():
    """Compare market maker vs retail execution profiles."""
    print("=== Execution Profile Comparison ===\n")

    combo, call_option, put_option = create_enhanced_test_combo()
    scenarios = create_market_data_scenarios()

    profiles = ["retail", "market_maker"]

    for scenario_name, scenario_data in scenarios.items():
        cache = setup_cache_with_scenario(scenario_data)
        cache.add_instrument(call_option)
        cache.add_instrument(put_option)
        cache.add_instrument(combo)

        print(f"{scenario_name.upper()} Scenario:")

        # Calculate natural combo price
        call_mid = (scenario_data["call_quote"].bid_price + scenario_data["call_quote"].ask_price) / 2
        put_mid = (scenario_data["put_quote"].bid_price + scenario_data["put_quote"].ask_price) / 2
        natural_price = call_mid - put_mid

        order = create_test_order(combo, Price.from_str(str(natural_price)))

        for profile in profiles:
            fill_model = ComboFillModel(
                base_fill_prob=0.8,
                liquidity_factor=0.7,
                execution_profile=profile,
                volatility_adjustment=True,
                time_of_day_adjustment=False,  # Disable to isolate profile effects
            )

            fill_prob, _ = fill_model.calculate_combo_fill_probability(order, combo, cache)
            print(f"  {profile.title():>12}: {fill_prob:.3f}")

        print()


if __name__ == "__main__":
    test_enhanced_fill_model_scenarios()
    test_time_of_day_variations()
    test_execution_profile_comparison()
    print("Enhanced fill model testing completed!")
