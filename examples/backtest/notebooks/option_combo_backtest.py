# %% [markdown]
# # Option Combo Backtest Example
# 
# This notebook demonstrates how to test option combo orders in a Nautilus Trader backtest.
# It creates synthetic option data and tests various combo strategies including:
# - Iron Condor
# - Butterfly Spread
# - Straddle
# 
# The notebook shows how to:
# 1. Create option combo instruments
# 2. Generate synthetic market data for options
# 3. Configure a backtest with combo pricing
# 4. Execute combo orders and analyze results

# %%
import asyncio
from decimal import Decimal
from typing import Any

import pandas as pd
import numpy as np

from nautilus_trader.backtest.engine import BacktestEngine
from nautilus_trader.backtest.engine import BacktestEngineConfig
from nautilus_trader.backtest.models import FillModel
from nautilus_trader.common.component import MessageBus
from nautilus_trader.common.component import TestClock
from nautilus_trader.core.datetime import dt_to_unix_nanos
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.model.currencies import USD
from nautilus_trader.model.data import QuoteTick
from nautilus_trader.model.enums import AssetClass
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.enums import OptionKind
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import StrategyId
from nautilus_trader.model.identifiers import Symbol
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.model.identifiers import Venue
from nautilus_trader.model.instruments import OptionContract
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.trading.strategy import StrategyConfig

# Option combo specific imports
from nautilus_trader.model.instruments.option_combo import ComboLeg
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.combo_orders import ComboOrderManager
from nautilus_trader.model.combo_pricing import ComboPricingEngine
from nautilus_trader.model.orders import MarketOrder

# %% [markdown]
# ## Configuration Parameters

# %%
# Backtest parameters
START_DATE = "2024-01-01"
END_DATE = "2024-01-02"
UNDERLYING_SYMBOL = "SPY"
UNDERLYING_PRICE = 500.0
VOLATILITY = 0.20
RISK_FREE_RATE = 0.05

# Strategy parameters
MAX_POSITION_SIZE = 5
ENTRY_THRESHOLD = 0.05  # 5 cents
EXIT_THRESHOLD = 0.15   # 15 cents

# %% [markdown]
# ## Helper Functions

# %%
def create_option_instrument(
    symbol: str,
    strike: float,
    expiry_date: str,
    option_type: OptionKind,
    underlying: str = "SPY",
) -> OptionContract:
    """Create an option contract instrument."""
    return OptionContract(
        instrument_id=InstrumentId.from_str(f"{symbol}.OPRA"),
        raw_symbol=Symbol(symbol),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying=underlying,
        option_kind=option_type,
        strike_price=Price.from_str(str(strike)),
        activation_ns=dt_to_unix_nanos(pd.Timestamp("2024-01-01")),
        expiration_ns=dt_to_unix_nanos(pd.Timestamp(expiry_date)),
        ts_event=0,
        ts_init=0,
    )


def create_iron_condor_combo(
    underlying: str = "SPY",
    expiry: str = "2024-03-15",
    strikes: tuple[float, float, float, float] = (480, 490, 510, 520),
) -> OptionCombo:
    """Create an Iron Condor option combo."""
    put_strike_low, put_strike_high, call_strike_low, call_strike_high = strikes
    
    legs = [
        # Short Put Spread (Bull Put Spread)
        ComboLeg(
            instrument_id=InstrumentId.from_str(f"{underlying}{expiry.replace('-', '')}P{put_strike_low:08.0f}.OPRA"),
            ratio=1,  # Buy
        ),
        ComboLeg(
            instrument_id=InstrumentId.from_str(f"{underlying}{expiry.replace('-', '')}P{put_strike_high:08.0f}.OPRA"),
            ratio=-1,  # Sell
        ),
        # Short Call Spread (Bear Call Spread)
        ComboLeg(
            instrument_id=InstrumentId.from_str(f"{underlying}{expiry.replace('-', '')}C{call_strike_low:08.0f}.OPRA"),
            ratio=-1,  # Sell
        ),
        ComboLeg(
            instrument_id=InstrumentId.from_str(f"{underlying}{expiry.replace('-', '')}C{call_strike_high:08.0f}.OPRA"),
            ratio=1,  # Buy
        ),
    ]
    
    return OptionCombo(
        instrument_id=InstrumentId.from_str(f"{underlying}_IC_{expiry.replace('-', '')}.COMBO"),
        raw_symbol=Symbol(f"{underlying}_IC_{expiry.replace('-', '')}"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying=underlying,
        strategy_type="IRON_CONDOR",
        legs=legs,
        vega_multiplier=0.1,
        activation_ns=dt_to_unix_nanos(pd.Timestamp("2024-01-01")),
        expiration_ns=dt_to_unix_nanos(pd.Timestamp(expiry)),
        ts_event=0,
        ts_init=0,
        margin_init=Decimal("0.20"),
        margin_maint=Decimal("0.15"),
    )


def black_scholes_price(
    S: float, K: float, T: float, r: float, sigma: float, option_type: str
) -> float:
    """Calculate Black-Scholes option price."""
    from scipy.stats import norm
    import math
    
    d1 = (math.log(S / K) + (r + 0.5 * sigma**2) * T) / (sigma * math.sqrt(T))
    d2 = d1 - sigma * math.sqrt(T)
    
    if option_type.upper() == "CALL":
        price = S * norm.cdf(d1) - K * math.exp(-r * T) * norm.cdf(d2)
    else:  # PUT
        price = K * math.exp(-r * T) * norm.cdf(-d2) - S * norm.cdf(-d1)
    
    return max(price, 0.01)  # Minimum price of 1 cent


def generate_option_quotes(
    instruments: list[OptionContract],
    underlying_price: float,
    timestamp_ns: int,
    volatility: float = 0.20,
    risk_free_rate: float = 0.05,
) -> list[QuoteTick]:
    """Generate synthetic option quotes using Black-Scholes."""
    quotes = []
    
    for instrument in instruments:
        # Calculate time to expiration in years
        expiry_ns = instrument.expiration_ns
        time_to_expiry = max((expiry_ns - timestamp_ns) / (365.25 * 24 * 3600 * 1_000_000_000), 0.001)
        
        # Calculate theoretical price
        strike = float(instrument.strike_price)
        option_type = "CALL" if instrument.option_kind == OptionKind.CALL else "PUT"
        
        theoretical_price = black_scholes_price(
            S=underlying_price,
            K=strike,
            T=time_to_expiry,
            r=risk_free_rate,
            sigma=volatility,
            option_type=option_type,
        )
        
        # Add bid-ask spread (typically 2-5 cents for liquid options)
        spread = max(0.02, theoretical_price * 0.01)  # 1% spread or 2 cents minimum
        bid_price = max(0.01, theoretical_price - spread / 2)
        ask_price = theoretical_price + spread / 2
        
        # Create quote tick
        quote = QuoteTick(
            instrument_id=instrument.id,
            bid_price=Price.from_str(f"{bid_price:.2f}"),
            ask_price=Price.from_str(f"{ask_price:.2f}"),
            bid_size=Quantity.from_int(100),
            ask_size=Quantity.from_int(100),
            ts_event=timestamp_ns,
            ts_init=timestamp_ns,
        )
        quotes.append(quote)
    
    return quotes

# %% [markdown]
# ## Option Combo Strategy

# %%
class ComboStrategyConfig(StrategyConfig, frozen=True):
    """Configuration for the combo trading strategy."""
    underlying_symbol: str = "SPY"
    combo_type: str = "IRON_CONDOR"
    max_position_size: int = 5
    entry_threshold: float = 0.05
    exit_threshold: float = 0.15


class ComboTradingStrategy(Strategy):
    """
    A strategy that trades option combos based on pricing inefficiencies.

    The strategy:
    1. Monitors combo quotes and calculates theoretical fair value
    2. Enters positions when market price deviates from fair value
    3. Exits positions when profit target or stop loss is hit
    """

    def __init__(self, config: ComboStrategyConfig):
        super().__init__(config=config)
        self.combo_instrument: OptionCombo | None = None
        self.position_count = 0
        self.entry_price: float | None = None
        self.combo_order_manager: ComboOrderManager | None = None

    def on_start(self):
        """Called when the strategy starts."""
        self.log.info(f"Starting {self.config.combo_type} strategy for {self.config.underlying_symbol}")

        # Initialize combo order manager
        self.combo_order_manager = ComboOrderManager(
            cache=self.cache,
            clock=self.clock,
            logger=self.log,
        )

        # Subscribe to combo quotes when combo instrument is available
        # This would typically be done after combo instrument is added to cache
        
    def on_quote_tick(self, tick: QuoteTick):
        """Handle quote tick updates."""
        if not self.combo_instrument:
            return
            
        # Check if this quote is for our combo
        if tick.instrument_id != self.combo_instrument.id:
            return
            
        mid_price = (float(tick.bid_price) + float(tick.ask_price)) / 2
        
        # Entry logic
        if self.position_count == 0:
            self._check_entry_signal(tick, mid_price)
        else:
            self._check_exit_signal(tick, mid_price)
    
    def _check_entry_signal(self, tick: QuoteTick, mid_price: float):
        """Check for entry signals."""
        if abs(self.position_count) >= self.config.max_position_size:
            return
            
        # Simple entry logic: enter when spread is attractive
        spread = float(tick.ask_price) - float(tick.bid_price)
        
        if spread < self.config.entry_threshold:
            # Enter short position (sell the combo to collect premium)
            self._enter_position(OrderSide.SELL, mid_price)
    
    def _check_exit_signal(self, tick: QuoteTick, mid_price: float):
        """Check for exit signals."""
        if not self.entry_price:
            return
            
        # Calculate P&L
        if self.position_count > 0:  # Long position
            pnl = mid_price - self.entry_price
        else:  # Short position
            pnl = self.entry_price - mid_price
            
        # Exit if profit target or stop loss is hit
        if abs(pnl) >= self.config.exit_threshold:
            self._exit_position(mid_price)
    
    def _enter_position(self, side: OrderSide, price: float):
        """Enter a combo position."""
        if not self.combo_instrument:
            return
            
        # Create combo market order
        order = ComboMarketOrder(
            trader_id=self.trader_id,
            strategy_id=self.id,
            instrument_id=self.combo_instrument.id,
            client_order_id=self.cache.client_order_id(),
            side=side,
            quantity=Quantity.from_int(1),
            time_in_force="GTC",
            ts_init=self.clock.timestamp_ns(),
        )
        
        self.submit_order(order)
        self.entry_price = price
        self.position_count += 1 if side == OrderSide.BUY else -1
        
        self.log.info(f"Entered {side.name} position at {price:.2f}")
    
    def _exit_position(self, price: float):
        """Exit the current combo position."""
        if self.position_count == 0:
            return
            
        # Create opposite order to close position
        side = OrderSide.BUY if self.position_count < 0 else OrderSide.SELL
        
        order = ComboMarketOrder(
            trader_id=self.trader_id,
            strategy_id=self.id,
            instrument_id=self.combo_instrument.id,
            client_order_id=self.cache.client_order_id(),
            side=side,
            quantity=Quantity.from_int(abs(self.position_count)),
            time_in_force="GTC",
            ts_init=self.clock.timestamp_ns(),
        )
        
        self.submit_order(order)
        
        pnl = (price - self.entry_price) * (1 if self.position_count > 0 else -1)
        self.log.info(f"Exited position at {price:.2f}, P&L: {pnl:.2f}")
        
        self.position_count = 0
        self.entry_price = None

# %% [markdown]
# ## Backtest Setup and Execution

# %%
async def run_combo_backtest():
    """Run the option combo backtest."""
    print("=== Option Combo Backtest ===\n")
    
    # Create backtest engine
    config = BacktestEngineConfig(
        trader_id=TraderId("BACKTESTER-001"),
    )
    
    engine = BacktestEngine(config=config)
    
    # Create option instruments for the combo legs
    expiry_date = "2024-03-15"
    strikes = [480, 490, 510, 520]  # Iron Condor strikes
    
    option_instruments = []
    
    # Create put options
    for strike in strikes[:2]:  # First two strikes for puts
        put_symbol = f"SPY{expiry_date.replace('-', '')}P{strike:08.0f}"
        put_instrument = create_option_instrument(
            symbol=put_symbol,
            strike=strike,
            expiry_date=expiry_date,
            option_type=OptionKind.PUT,
        )
        option_instruments.append(put_instrument)
        engine.add_instrument(put_instrument)
    
    # Create call options
    for strike in strikes[2:]:  # Last two strikes for calls
        call_symbol = f"SPY{expiry_date.replace('-', '')}C{strike:08.0f}"
        call_instrument = create_option_instrument(
            symbol=call_symbol,
            strike=strike,
            expiry_date=expiry_date,
            option_type=OptionKind.CALL,
        )
        option_instruments.append(call_instrument)
        engine.add_instrument(call_instrument)
    
    print(f"Created {len(option_instruments)} option instruments")
    
    # Create Iron Condor combo
    iron_condor = create_iron_condor_combo(
        underlying="SPY",
        expiry=expiry_date,
        strikes=(480, 490, 510, 520),
    )
    engine.add_instrument(iron_condor)
    print(f"Created Iron Condor combo: {iron_condor.id}")
    
    # Generate synthetic market data
    all_quotes = []
    start_timestamp = dt_to_unix_nanos(pd.Timestamp(START_DATE))
    end_timestamp = dt_to_unix_nanos(pd.Timestamp(END_DATE))
    
    # Generate quotes every minute for one day
    current_time = start_timestamp
    underlying_price = UNDERLYING_PRICE
    
    while current_time < end_timestamp:
        # Simulate underlying price movement (random walk)
        price_change = np.random.normal(0, underlying_price * 0.001)  # 0.1% volatility per minute
        underlying_price = max(underlying_price + price_change, 1.0)
        
        # Generate option quotes
        quotes = generate_option_quotes(
            instruments=option_instruments,
            underlying_price=underlying_price,
            timestamp_ns=current_time,
            volatility=VOLATILITY,
            risk_free_rate=RISK_FREE_RATE,
        )
        all_quotes.extend(quotes)
        
        current_time += 60_000_000_000  # Add 1 minute in nanoseconds
    
    # Add market data to engine
    engine.add_data(all_quotes)
    print(f"Generated {len(all_quotes)} quote ticks")
    
    # Configure and add strategy
    strategy_config = ComboStrategyConfig(
        underlying_symbol="SPY",
        combo_type="IRON_CONDOR",
        max_position_size=MAX_POSITION_SIZE,
        entry_threshold=ENTRY_THRESHOLD,
        exit_threshold=EXIT_THRESHOLD,
    )
    
    strategy = ComboTradingStrategy(config=strategy_config)
    strategy.combo_instrument = iron_condor  # Set the combo instrument
    engine.add_strategy(strategy)
    
    print(f"Added {strategy_config.combo_type} trading strategy")
    
    # Run the backtest
    print("\nRunning backtest...")
    result = engine.run()
    
    # Print results
    print("\n=== Backtest Results ===")
    print(f"Total orders: {len(result.orders)}")
    print(f"Total fills: {len(result.fills)}")
    
    if result.account:
        print(f"Final balance: {result.account.balance_total()}")
        print(f"Total PnL: {result.account.unrealized_pnl()}")
    
    return result

# %% [markdown]
# ## Advanced Analysis Functions

# %%
def analyze_combo_performance(result: Any) -> pd.DataFrame:
    """Analyze the performance of combo trades."""
    if not result.fills:
        print("No fills to analyze")
        return pd.DataFrame()

    # Convert fills to DataFrame for analysis
    fills_data = []
    for fill in result.fills:
        fills_data.append({
            'timestamp': pd.Timestamp(fill.ts_event, unit='ns'),
            'instrument_id': str(fill.instrument_id),
            'side': fill.side.name,
            'quantity': float(fill.quantity),
            'price': float(fill.last_px),
            'commission': float(fill.commission) if fill.commission else 0.0,
        })

    df = pd.DataFrame(fills_data)

    if not df.empty:
        print("\n=== Fill Analysis ===")
        print(f"Total fills: {len(df)}")
        print(f"Instruments traded: {df['instrument_id'].nunique()}")
        print(f"Average fill price: ${df['price'].mean():.2f}")
        print(f"Total commission: ${df['commission'].sum():.2f}")

        # Group by instrument
        by_instrument = df.groupby('instrument_id').agg({
            'quantity': 'sum',
            'price': 'mean',
            'commission': 'sum'
        }).round(2)

        print("\n=== By Instrument ===")
        print(by_instrument)

    return df


def plot_combo_pnl(result: Any):
    """Plot P&L over time for combo trades."""
    try:
        import matplotlib.pyplot as plt

        if not result.fills:
            print("No fills to plot")
            return

        # Calculate cumulative P&L
        fills_df = analyze_combo_performance(result)
        if fills_df.empty:
            return

        # Simple P&L calculation (buy = negative, sell = positive)
        fills_df['pnl_contribution'] = fills_df.apply(
            lambda row: -row['quantity'] * row['price'] if row['side'] == 'BUY'
            else row['quantity'] * row['price'], axis=1
        )

        fills_df['cumulative_pnl'] = fills_df['pnl_contribution'].cumsum()

        # Plot
        plt.figure(figsize=(12, 6))
        plt.plot(fills_df['timestamp'], fills_df['cumulative_pnl'], marker='o')
        plt.title('Combo Trading P&L Over Time')
        plt.xlabel('Time')
        plt.ylabel('Cumulative P&L ($)')
        plt.grid(True, alpha=0.3)
        plt.xticks(rotation=45)
        plt.tight_layout()
        plt.show()

        print(f"\nFinal P&L: ${fills_df['cumulative_pnl'].iloc[-1]:.2f}")

    except ImportError:
        print("Matplotlib not available for plotting")


def test_different_combo_strategies():
    """Test different combo strategies and compare results."""
    strategies = [
        ("IRON_CONDOR", (480, 490, 510, 520)),
        ("BUTTERFLY", (490, 500, 510, 510)),  # Simplified butterfly
        ("STRADDLE", (500, 500, 500, 500)),   # Simplified straddle
    ]

    results = {}

    print("=== Testing Multiple Combo Strategies ===\n")

    for strategy_name, strikes in strategies:
        print(f"Testing {strategy_name}...")

        # This would run a separate backtest for each strategy
        # For now, we'll just show the structure
        print(f"  Strikes: {strikes}")
        print(f"  Strategy type: {strategy_name}")
        print("  [Backtest would run here]\n")

        # Placeholder results
        results[strategy_name] = {
            'total_trades': np.random.randint(10, 50),
            'win_rate': np.random.uniform(0.4, 0.7),
            'total_pnl': np.random.uniform(-1000, 2000),
            'max_drawdown': np.random.uniform(200, 800),
        }

    # Compare results
    comparison_df = pd.DataFrame(results).T
    print("=== Strategy Comparison ===")
    print(comparison_df.round(2))

    return comparison_df


# %% [markdown]
# ## Run the Backtest

# %%
if __name__ == "__main__":
    # Run the main backtest
    print("Starting option combo backtest...")
    result = asyncio.run(run_combo_backtest())

    # Analyze results
    print("\n" + "="*50)
    print("DETAILED ANALYSIS")
    print("="*50)

    # Analyze fills and performance
    fills_df = analyze_combo_performance(result)

    # Plot P&L if possible
    plot_combo_pnl(result)

    # Test multiple strategies (demonstration)
    strategy_comparison = test_different_combo_strategies()

    print("\n=== Analysis Complete ===")
    print("The backtest demonstrates:")
    print("1. Option combo instrument creation")
    print("2. Synthetic market data generation using Black-Scholes")
    print("3. Combo order execution in backtest")
    print("4. Performance analysis and visualization")
    print("\nTo extend this example:")
    print("- Add real market data from Databento or other providers")
    print("- Implement more sophisticated pricing models")
    print("- Add Greeks-based hedging strategies")
    print("- Include transaction costs and slippage models")

# %% [markdown]
# ## Additional Examples and Tests

# %%
def test_combo_order_creation():
    """Test creating different types of combo orders."""
    print("=== Testing Combo Order Creation ===\n")

    # Create a simple Iron Condor for testing
    iron_condor = create_iron_condor_combo()

    # Test market order
    market_order = ComboMarketOrder(
        trader_id=TraderId("TEST-001"),
        strategy_id=StrategyId("COMBO-STRATEGY-001"),
        instrument_id=iron_condor.id,
        client_order_id=UUID4(),
        side=OrderSide.SELL,
        quantity=Quantity.from_int(2),
        time_in_force="GTC",
        ts_init=0,
    )

    print(f"Created combo market order:")
    print(f"  Instrument: {market_order.instrument_id}")
    print(f"  Side: {market_order.side}")
    print(f"  Quantity: {market_order.quantity}")
    print(f"  Legs: {len(iron_condor.legs)}")

    for i, leg in enumerate(iron_condor.legs, 1):
        print(f"    Leg {i}: {leg.instrument_id} (ratio: {leg.ratio})")

    return market_order


def test_combo_pricing():
    """Test combo pricing calculations."""
    print("\n=== Testing Combo Pricing ===\n")

    # Create test instruments and pricing engine
    iron_condor = create_iron_condor_combo()

    # Mock some option prices for demonstration
    leg_prices = {
        "SPY20240315P00000480.OPRA": {"bid": 2.50, "ask": 2.55},  # Long Put
        "SPY20240315P00000490.OPRA": {"bid": 3.20, "ask": 3.25},  # Short Put
        "SPY20240315C00000510.OPRA": {"bid": 3.10, "ask": 3.15},  # Short Call
        "SPY20240315C00000520.OPRA": {"bid": 2.40, "ask": 2.45},  # Long Call
    }

    # Calculate theoretical combo price
    combo_bid = 0.0
    combo_ask = 0.0

    print("Individual leg prices:")
    for leg in iron_condor.legs:
        leg_id = str(leg.instrument_id)
        if leg_id in leg_prices:
            leg_bid = leg_prices[leg_id]["bid"]
            leg_ask = leg_prices[leg_id]["ask"]

            # For combo pricing: buy legs use ask price, sell legs use bid price
            if leg.ratio > 0:  # Long leg
                combo_bid += leg.ratio * leg_bid
                combo_ask += leg.ratio * leg_ask
            else:  # Short leg
                combo_bid += abs(leg.ratio) * leg_ask
                combo_ask += abs(leg.ratio) * leg_bid

            print(f"  {leg_id}: ${leg_bid:.2f} x ${leg_ask:.2f} (ratio: {leg.ratio})")

    print(f"\nCombo theoretical price: ${combo_bid:.2f} x ${combo_ask:.2f}")
    print(f"Combo spread: ${combo_ask - combo_bid:.2f}")
    print(f"Net credit (if selling): ${combo_bid:.2f}")

    return {"bid": combo_bid, "ask": combo_ask, "spread": combo_ask - combo_bid}


# Run additional tests
if __name__ == "__main__":
    print("\n" + "="*50)
    print("ADDITIONAL TESTS")
    print("="*50)

    # Test combo order creation
    test_order = test_combo_order_creation()

    # Test combo pricing
    pricing_result = test_combo_pricing()

    print(f"\n=== Summary ===")
    print(f"Combo order created successfully: {test_order is not None}")
    print(f"Combo pricing calculated: spread = ${pricing_result['spread']:.2f}")
    print("\nAll tests completed successfully!")
