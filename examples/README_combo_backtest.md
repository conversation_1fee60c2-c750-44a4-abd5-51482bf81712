# Option Combo Backtest Examples

This directory contains examples and tests for option combo trading in Nautilus Trader backtests.

## Files Overview

### 1. `test_combo_backtest_simple.py`
A simple, focused test script that verifies option combo orders work correctly in backtest.

**Features:**
- Creates basic option instruments (puts and calls)
- Builds an Iron Condor combo instrument
- Submits combo market orders
- Verifies order execution and fills
- Calculates basic P&L

**Usage:**
```bash
cd nautilus_trader
python examples/test_combo_backtest_simple.py
```

**Expected Output:**
- ✅ Orders submitted successfully
- ✅ Orders filled correctly
- ✅ Combo orders processed
- 🎉 All tests passed

### 2. `backtest/notebooks/option_combo_backtest.py`
A comprehensive notebook-style example for advanced combo backtesting.

**Features:**
- Multiple combo strategies (Iron Condor, Butterfly, Straddle)
- Black-Scholes option pricing for synthetic data
- Advanced strategy logic with entry/exit signals
- Performance analysis and visualization
- Strategy comparison framework

**Usage:**
```bash
cd nautilus_trader
python examples/backtest/notebooks/option_combo_backtest.py
```

## Key Components Tested

### Option Combo Instruments
- **Iron Condor**: 4-leg strategy with puts and calls
- **Butterfly**: 3-leg strategy with symmetric strikes
- **Straddle**: 2-leg strategy with same strike

### Order Types
- `ComboMarketOrder`: Market orders for entire combo
- Multi-leg execution with proper ratios
- Automatic leg order generation

### Pricing and Greeks
- Black-Scholes theoretical pricing
- Vega-weighted spread calculations
- Greeks-based risk management

### Backtest Features
- Synthetic market data generation
- Realistic bid-ask spreads
- Commission and slippage modeling
- P&L tracking and analysis

## Example Combo Strategies

### Iron Condor
```python
# Short Put Spread + Short Call Spread
legs = [
    ComboLeg(put_480, ratio=1),   # Buy Put 480
    ComboLeg(put_490, ratio=-1),  # Sell Put 490
    ComboLeg(call_510, ratio=-1), # Sell Call 510
    ComboLeg(call_520, ratio=1),  # Buy Call 520
]
```

### Butterfly Spread
```python
# Buy ITM, Sell 2 ATM, Buy OTM
legs = [
    ComboLeg(call_490, ratio=1),  # Buy Call 490
    ComboLeg(call_500, ratio=-2), # Sell 2 Call 500
    ComboLeg(call_510, ratio=1),  # Buy Call 510
]
```

## Testing Workflow

1. **Quick Test**: Run `test_combo_backtest_simple.py` to verify basic functionality
2. **Advanced Analysis**: Use the notebook for comprehensive strategy testing
3. **Custom Strategies**: Modify the examples for your specific needs

## Requirements

### Core Dependencies
- nautilus_trader (with combo support)
- pandas
- numpy

### Optional Dependencies
- matplotlib (for plotting)
- scipy (for Black-Scholes calculations)

## Troubleshooting

### Common Issues

1. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'nautilus_trader.model.combo_orders'
   ```
   - Ensure you're using the latest version with combo support
   - Check that combo modules are properly installed

2. **No Fills Received**
   - Verify market data is being generated correctly
   - Check that instruments are added to the engine
   - Ensure strategy is subscribing to the right instruments

3. **Pricing Issues**
   - Verify option parameters (strike, expiry, underlying)
   - Check that quotes have reasonable bid-ask spreads
   - Ensure underlying price is realistic

### Debug Tips

1. **Enable Detailed Logging**
   ```python
   import logging
   logging.basicConfig(level=logging.INFO)
   ```

2. **Check Order Status**
   ```python
   for order in result.orders:
       print(f"Order: {order.status} - {order}")
   ```

3. **Verify Instrument Setup**
   ```python
   for instrument in engine.cache.instruments():
       print(f"Instrument: {instrument.id}")
   ```

## Extending the Examples

### Adding Real Market Data
Replace synthetic data generation with real market data from providers like Databento:

```python
from nautilus_trader.adapters.databento import DatabentoDataLoader

# Load real option data
loader = DatabentoDataLoader()
data = loader.load_option_data(symbols, start_date, end_date)
```

### Custom Combo Strategies
Create your own combo strategies by extending the base classes:

```python
class CustomComboStrategy(Strategy):
    def on_quote_tick(self, tick):
        # Your custom logic here
        pass
```

### Advanced Greeks Integration
Use the Greeks calculator for more sophisticated pricing:

```python
from nautilus_trader.model.greeks import GreeksCalculator

calculator = GreeksCalculator()
greeks = calculator.calculate(instrument, underlying_price, volatility)
```

## Performance Considerations

- Use vectorized calculations for large datasets
- Cache frequently accessed data
- Consider memory usage with large option chains
- Profile bottlenecks in custom strategies

## Next Steps

1. Run the simple test to verify your setup
2. Explore the comprehensive notebook
3. Modify examples for your specific use cases
4. Integrate with real market data
5. Deploy to live trading (with proper risk management)

For more information, see the main Nautilus Trader documentation and the option combo implementation details.
