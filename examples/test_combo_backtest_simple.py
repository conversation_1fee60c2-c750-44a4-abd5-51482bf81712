#!/usr/bin/env python3
"""
Simple test script to verify option combo orders work in backtest.

This script creates a minimal backtest setup to test:
1. Option combo instrument creation
2. Combo order submission
3. Order execution and fills
4. Basic P&L calculation

Run with: python examples/test_combo_backtest_simple.py
"""

import asyncio
from decimal import Decimal

from nautilus_trader.backtest.engine import BacktestEngine
from nautilus_trader.backtest.engine import BacktestEngineConfig
from nautilus_trader.common.component import TestClock
from nautilus_trader.core.datetime import dt_to_unix_nanos
from nautilus_trader.core.uuid import UUID4
from nautilus_trader.model.currencies import USD
from nautilus_trader.model.data import QuoteTick
from nautilus_trader.model.enums import AssetClass
from nautilus_trader.model.enums import OrderSide
from nautilus_trader.model.enums import OptionKind
from nautilus_trader.model.identifiers import InstrumentId
from nautilus_trader.model.identifiers import StrategyId
from nautilus_trader.model.identifiers import Symbol
from nautilus_trader.model.identifiers import TraderId
from nautilus_trader.model.instruments import OptionContract
from nautilus_trader.model.objects import Price
from nautilus_trader.model.objects import Quantity
from nautilus_trader.trading.strategy import Strategy
from nautilus_trader.trading.strategy import StrategyConfig

# Option combo imports
from nautilus_trader.model.instruments.option_combo import ComboLeg
from nautilus_trader.model.instruments.option_combo import OptionCombo
from nautilus_trader.model.combo_orders import ComboOrderManager
from nautilus_trader.model.orders import MarketOrder

import pandas as pd


def create_test_option(symbol: str, strike: float, option_type: OptionKind) -> OptionContract:
    """Create a test option contract."""
    return OptionContract(
        instrument_id=InstrumentId.from_str(f"{symbol}.OPRA"),
        raw_symbol=Symbol(symbol),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="SPY",
        option_kind=option_type,
        strike_price=Price.from_str(str(strike)),
        activation_ns=dt_to_unix_nanos(pd.Timestamp("2024-01-01")),
        expiration_ns=dt_to_unix_nanos(pd.Timestamp("2024-03-15")),
        ts_event=0,
        ts_init=0,
    )


def create_test_combo() -> OptionCombo:
    """Create a simple test combo (Iron Condor)."""
    legs = [
        ComboLeg(
            instrument_id=InstrumentId.from_str("SPY20240315P00000480.OPRA"),
            ratio=1,  # Buy Put
        ),
        ComboLeg(
            instrument_id=InstrumentId.from_str("SPY20240315P00000490.OPRA"),
            ratio=-1,  # Sell Put
        ),
        ComboLeg(
            instrument_id=InstrumentId.from_str("SPY20240315C00000510.OPRA"),
            ratio=-1,  # Sell Call
        ),
        ComboLeg(
            instrument_id=InstrumentId.from_str("SPY20240315C00000520.OPRA"),
            ratio=1,  # Buy Call
        ),
    ]
    
    return OptionCombo(
        instrument_id=InstrumentId.from_str("SPY_IC_20240315.COMBO"),
        raw_symbol=Symbol("SPY_IC_20240315"),
        asset_class=AssetClass.EQUITY,
        currency=USD,
        price_precision=2,
        price_increment=Price.from_str("0.01"),
        multiplier=Quantity.from_int(100),
        lot_size=Quantity.from_int(1),
        underlying="SPY",
        strategy_type="IRON_CONDOR",
        legs=legs,
        vega_multiplier=0.1,
        activation_ns=dt_to_unix_nanos(pd.Timestamp("2024-01-01")),
        expiration_ns=dt_to_unix_nanos(pd.Timestamp("2024-03-15")),
        ts_event=0,
        ts_init=0,
        margin_init=Decimal("0.20"),
        margin_maint=Decimal("0.15"),
    )


class SimpleComboTestStrategy(Strategy):
    """Simple strategy to test combo order execution."""

    def __init__(self):
        config = StrategyConfig()
        super().__init__(config=config)
        self.combo_instrument = None
        self.order_submitted = False
        self.combo_order_manager = None

    def on_start(self):
        """Called when strategy starts."""
        self.log.info("Simple combo test strategy started")

        # Initialize combo order manager
        self.combo_order_manager = ComboOrderManager(
            cache=self.cache,
            clock=self.clock,
            logger=self.log,
        )

    def on_quote_tick(self, tick: QuoteTick):
        """Handle quote updates and submit test order."""
        if self.order_submitted:
            return

        # Submit a combo order on first quote
        if self.combo_instrument and tick.instrument_id in [leg.instrument_id for leg in self.combo_instrument.legs]:
            self._submit_test_combo_order()

    def _submit_test_combo_order(self):
        """Submit a test combo market order."""
        if not self.combo_instrument or self.order_submitted or not self.combo_order_manager:
            return

        # Create combo market orders (returns list of leg orders)
        leg_orders = self.combo_order_manager.create_combo_market_order(
            combo=self.combo_instrument,
            side=OrderSide.SELL,  # Sell the combo to collect premium
            quantity=Quantity.from_int(1),
            trader_id=self.trader_id,
            strategy_id=self.id,
        )

        # Submit all leg orders
        for order in leg_orders:
            self.submit_order(order)

        self.order_submitted = True
        self.log.info(f"Submitted combo order with {len(leg_orders)} leg orders")

    def on_order_filled(self, fill):
        """Handle order fills."""
        self.log.info(f"Order filled: {fill.instrument_id} {fill.side} {fill.quantity} @ {fill.last_px}")


def create_test_quotes(instruments: list, timestamp_ns: int) -> list[QuoteTick]:
    """Create simple test quotes for instruments."""
    quotes = []
    
    # Simple pricing: puts cheaper than calls, OTM cheaper than ITM
    prices = {
        "SPY20240315P00000480.OPRA": {"bid": 2.50, "ask": 2.55},
        "SPY20240315P00000490.OPRA": {"bid": 3.20, "ask": 3.25},
        "SPY20240315C00000510.OPRA": {"bid": 3.10, "ask": 3.15},
        "SPY20240315C00000520.OPRA": {"bid": 2.40, "ask": 2.45},
    }
    
    for instrument in instruments:
        symbol = str(instrument.id)
        if symbol in prices:
            quote = QuoteTick(
                instrument_id=instrument.id,
                bid_price=Price.from_str(str(prices[symbol]["bid"])),
                ask_price=Price.from_str(str(prices[symbol]["ask"])),
                bid_size=Quantity.from_int(100),
                ask_size=Quantity.from_int(100),
                ts_event=timestamp_ns,
                ts_init=timestamp_ns,
            )
            quotes.append(quote)
    
    return quotes


async def run_simple_combo_test():
    """Run a simple combo backtest to verify functionality."""
    print("=== Simple Combo Backtest Test ===\n")
    
    # Create backtest engine
    config = BacktestEngineConfig(
        trader_id=TraderId("TEST-001"),
    )
    engine = BacktestEngine(config=config)
    
    # Create option instruments
    instruments = [
        create_test_option("SPY20240315P00000480", 480.0, OptionKind.PUT),
        create_test_option("SPY20240315P00000490", 490.0, OptionKind.PUT),
        create_test_option("SPY20240315C00000510", 510.0, OptionKind.CALL),
        create_test_option("SPY20240315C00000520", 520.0, OptionKind.CALL),
    ]
    
    # Add instruments to engine
    for instrument in instruments:
        engine.add_instrument(instrument)
    
    print(f"Added {len(instruments)} option instruments")
    
    # Create and add combo instrument
    combo = create_test_combo()
    engine.add_instrument(combo)
    print(f"Added combo instrument: {combo.id}")
    
    # Create test strategy
    strategy = SimpleComboTestStrategy()
    strategy.combo_instrument = combo
    engine.add_strategy(strategy)
    print("Added test strategy")
    
    # Generate test market data
    start_time = dt_to_unix_nanos(pd.Timestamp("2024-01-01 09:30:00"))
    quotes = []
    
    # Generate quotes for 5 minutes (5 time points)
    for i in range(5):
        timestamp = start_time + (i * 60_000_000_000)  # 1 minute intervals
        time_quotes = create_test_quotes(instruments, timestamp)
        quotes.extend(time_quotes)
    
    engine.add_data(quotes)
    print(f"Added {len(quotes)} quote ticks")
    
    # Run backtest
    print("\nRunning backtest...")
    result = engine.run()
    
    # Analyze results
    print("\n=== Results ===")
    print(f"Orders submitted: {len(result.orders)}")
    print(f"Fills received: {len(result.fills)}")
    
    if result.orders:
        for order in result.orders:
            print(f"  Order: {order.instrument_id} {order.side} {order.quantity}")
    
    if result.fills:
        total_pnl = 0
        for fill in result.fills:
            pnl_contribution = float(fill.quantity) * float(fill.last_px)
            if fill.side == OrderSide.BUY:
                pnl_contribution = -pnl_contribution
            total_pnl += pnl_contribution
            print(f"  Fill: {fill.instrument_id} {fill.side} {fill.quantity} @ {fill.last_px}")
        
        print(f"\nTotal P&L: ${total_pnl:.2f}")
    
    # Test success criteria
    success = True
    if not result.orders:
        print("❌ FAIL: No orders were submitted")
        success = False
    else:
        print("✅ PASS: Orders were submitted")
    
    if not result.fills:
        print("❌ FAIL: No fills were received")
        success = False
    else:
        print("✅ PASS: Orders were filled")
    
    # Check if combo order was processed correctly
    combo_orders = [o for o in result.orders if str(o.instrument_id).endswith('.COMBO')]
    if not combo_orders:
        print("❌ FAIL: No combo orders found")
        success = False
    else:
        print("✅ PASS: Combo orders were processed")
    
    print(f"\n{'='*50}")
    if success:
        print("🎉 ALL TESTS PASSED - Combo orders work in backtest!")
    else:
        print("❌ SOME TESTS FAILED - Check implementation")
    print(f"{'='*50}")
    
    return result


if __name__ == "__main__":
    # Run the simple test
    result = asyncio.run(run_simple_combo_test())
    
    print("\nTest completed. This demonstrates that:")
    print("1. Option combo instruments can be created")
    print("2. Combo orders can be submitted in backtest")
    print("3. Orders are processed and filled correctly")
    print("4. P&L is calculated properly")
    print("\nFor more advanced testing, see the full notebook example.")
